# 🏗️ **Wiz-Aroma System Architecture**

## **Multi-Bot Telegram Delivery Management Platform**

---

## 📊 **Architecture Overview**

```
┌─────────────────────────────────────────────────────────────────┐
│                    🌐 TELEGRAM BOT API                         │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    🤖 MULTI-BOT LAYER                          │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│ 🛍️ User Bot │ 👨‍💼 Admin Bot│ 💰 Finance  │ 🔧 Maint.   │ 📢 Notif│
│             │             │    Bot      │    Bot      │   Bot   │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    ⚙️ BUSINESS LOGIC LAYER                     │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────┤
│ Order       │ Transaction │ Delivery    │ Data        │ User    │
│ Management  │ Processing  │ Management  │ Management  │ Management│
└─────────────┴─────────────┴─────────────┴─────────────┴─────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    🗄️ DATA LAYER                               │
├─────────────────────┬───────────────────┬───────────────────────┤
│ 🔥 Firebase         │ 📁 Local Storage  │ 🚀 Redis Cache       │
│ Realtime Database   │ JSON Backup       │ (V-2.0 Planned)      │
└─────────────────────┴───────────────────┴───────────────────────┘
```

---

## 🤖 **Bot Architecture Details**

### **🛍️ User Bot** (`@wiz_aroma_bot`)

**Purpose**: Customer-facing interface for order placement and management

**Core Functions**:

- 📱 Order placement workflow
- 💫 Points system management
- ⭐ Favorite orders handling
- 📍 Delivery location management
- 💬 Customer support interface
- **Access**: Public - anyone can interact

**Handler Structure**:

```python
@user_bot.message_handler(commands=['start'])
@user_bot.callback_query_handler(func=lambda call: call.data.startswith("menu_"))
@user_bot.message_handler(content_types=['location'])
```

### **👨‍💼 Admin Bot** (`@Wiz_aroma_admin_bot`)

**Purpose**: Administrative oversight and order management

**Core Functions**:

- 📋 Order review and approval/rejection
- 🏪 Restaurant management
- 📊 System monitoring
- 👥 Customer communication
- 📈 Performance analytics
- **Access**: Restricted to authorized admin users

### **💰 Finance Bot** (`@Wiz_Aroma_Finance_bot`)

- **Purpose**: Handles payment verification
- **Features**:
  - Receiving payment receipts
  - Verifying payments
  - Tracking transactions
  - Processing refunds
- **Access**: Restricted to authorized finance users (configured in `.env`)

### Maintenance Bot (`@Wiz_Aroma_Maintenance_bot`)

- **Purpose**: Handles system configuration and maintenance
- **Features**:
  - Managing areas
  - Managing restaurants
  - Managing menus
  - Managing delivery locations and fees
  - System configuration
- **Access**: Restricted to authorized maintenance users (configured in `.env`)

## Data Flow

The system uses a combination of in-memory data and persistent storage:

1. **User Interaction**: Users interact with the User Bot to place orders
2. **Order Processing**: Orders are stored in memory and persisted to JSON files
3. **Admin Review**: Admin Bot receives new orders for review
4. **Payment Processing**: After admin approval, users submit payment through the User Bot
5. **Finance Verification**: Finance Bot receives payment receipts for verification
6. **Order Fulfillment**: After payment verification, the order is processed for delivery

## Component Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│    User Bot     │◄────┤    Admin Bot    │◄────┤   Finance Bot   │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                       Shared Data Storage                       │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
         ▲
         │
         │
┌────────┴────────┐
│                 │
│ Maintenance Bot │
│                 │
└─────────────────┘
```

## Data Storage

The system uses JSON files for persistent storage:

- **User Data**:
  - `user_points.json`: User points balance
  - `user_names.json`: User names
  - `user_phone_numbers.json`: User phone numbers
  - `user_order_history.json`: User order history
  - `favorite_orders.json`: User favorite orders

- **Order Data**:
  - `current_orders.json`: Current active orders
  - `order_status.json`: Status of orders in progress
  - `pending_admin_reviews.json`: Orders waiting for admin review
  - `admin_remarks.json`: Admin remarks on orders
  - `awaiting_receipt.json`: Orders waiting for payment receipt

- **Configuration Data**:
  - `areas.json`: Available areas
  - `restaurants.json`: Available restaurants
  - `menus.json`: Restaurant menus
  - `delivery_locations.json`: Available delivery locations
  - `delivery_fees.json`: Delivery fees for different locations

## Process Flow

1. **Order Placement**:
   - User selects area
   - User selects restaurant
   - User adds menu items
   - User provides delivery details
   - User confirms order

2. **Admin Review**:
   - Admin receives order notification
   - Admin reviews order details
   - Admin approves or rejects order
   - Admin can add remarks if needed

3. **Payment Processing**:
   - User receives approval notification
   - User selects payment method
   - User sends payment receipt
   - Finance receives payment receipt
   - Finance verifies payment

4. **Order Fulfillment**:
   - User receives payment confirmation
   - Order is processed for delivery
   - User can track order status

## Error Handling

The system includes comprehensive error handling:

- **Exception Handling**: All operations are wrapped in try-except blocks
- **Logging**: Detailed logging of all operations and errors
- **Data Consistency**: Regular checks to ensure data consistency
- **Backup**: Automatic backup of data files before writing

## Security

- **Authentication**: Only authorized users can access admin, finance, and maintenance bots
- **Environment Variables**: All sensitive information is stored in environment variables
- **Input Validation**: All user inputs are validated before processing
