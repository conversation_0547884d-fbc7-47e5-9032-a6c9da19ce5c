"""
Test suite for financial calculations with point-based payments.

This module tests the financial calculation utilities to ensure:
1. Delivery personnel receive 50% of delivery fees regardless of payment method
2. Company profit excludes delivery fees paid with points
3. Payment breakdown calculations are accurate for mixed payment methods
4. Backward compatibility with existing order data
"""

import unittest
import sys
import os

# Add the src directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.financial_calculations import (
    calculate_payment_breakdown,
    calculate_personnel_earnings,
    calculate_company_profit,
    calculate_revenue_breakdown,
    update_order_payment_breakdown
)


class TestFinancialCalculations(unittest.TestCase):
    """Test financial calculations for point-based payments"""

    def setUp(self):
        """Set up test data"""
        # Order with cash payment only
        self.cash_order = {
            'order_number': 'TEST001',
            'subtotal': 100.0,
            'delivery_fee': 30.0,
            'points_used': 0,
            'payment_method': 'Telebirr'
        }
        
        # Order with points used for delivery fee
        self.points_order = {
            'order_number': 'TEST002',
            'subtotal': 150.0,
            'delivery_fee': 25.0,
            'points_used': 25,
            'payment_method': 'Points+Regular'
        }
        
        # Order with partial points usage (more points than delivery fee)
        self.partial_points_order = {
            'order_number': 'TEST003',
            'subtotal': 80.0,
            'delivery_fee': 20.0,
            'points_used': 15,
            'payment_method': 'Points+Regular'
        }
        
        # Legacy order without new payment breakdown fields
        self.legacy_order = {
            'order_number': 'LEGACY001',
            'subtotal': 120.0,
            'delivery_fee': 35.0,
            'points_used': 35,
            'payment_method': 'Points+Regular'
        }

    def test_payment_breakdown_cash_only(self):
        """Test payment breakdown for cash-only orders"""
        breakdown = calculate_payment_breakdown(self.cash_order)
        
        self.assertEqual(breakdown['cash_amount'], 130.0)  # 100 + 30
        self.assertEqual(breakdown['points_amount'], 0)
        self.assertEqual(breakdown['delivery_fee_cash'], 30.0)
        self.assertEqual(breakdown['delivery_fee_points'], 0)
        self.assertEqual(breakdown['subtotal'], 100.0)

    def test_payment_breakdown_points_for_delivery(self):
        """Test payment breakdown when points cover full delivery fee"""
        breakdown = calculate_payment_breakdown(self.points_order)
        
        self.assertEqual(breakdown['cash_amount'], 150.0)  # Only subtotal
        self.assertEqual(breakdown['points_amount'], 25)
        self.assertEqual(breakdown['delivery_fee_cash'], 0)
        self.assertEqual(breakdown['delivery_fee_points'], 25)
        self.assertEqual(breakdown['subtotal'], 150.0)

    def test_payment_breakdown_partial_points(self):
        """Test payment breakdown when points partially cover delivery fee"""
        breakdown = calculate_payment_breakdown(self.partial_points_order)
        
        self.assertEqual(breakdown['cash_amount'], 85.0)  # 80 + 5 remaining delivery fee
        self.assertEqual(breakdown['points_amount'], 15)
        self.assertEqual(breakdown['delivery_fee_cash'], 5.0)  # 20 - 15
        self.assertEqual(breakdown['delivery_fee_points'], 15)
        self.assertEqual(breakdown['subtotal'], 80.0)

    def test_personnel_earnings_calculation(self):
        """Test that personnel earnings are 50% of all delivery fees regardless of payment method"""
        orders = [self.cash_order, self.points_order, self.partial_points_order]
        
        # Expected: 50% of (30 + 25 + 20) = 37.5
        expected_earnings = (30.0 + 25.0 + 20.0) * 0.5
        actual_earnings = calculate_personnel_earnings(orders)
        
        self.assertEqual(actual_earnings, expected_earnings)

    def test_company_profit_calculation_with_cost_accounting(self):
        """Test that company profit uses cost accounting: cash_revenue - point_payment_costs"""
        orders = [self.cash_order, self.points_order, self.partial_points_order]

        profit_data = calculate_company_profit(orders)

        # Total delivery fees: 30 + 25 + 20 = 75
        self.assertEqual(profit_data['total_delivery_fees'], 75.0)

        # Cash delivery fees: 30 + 0 + 5 = 35
        self.assertEqual(profit_data['cash_delivery_fees'], 35.0)

        # Points delivery fees: 0 + 25 + 15 = 40
        self.assertEqual(profit_data['points_delivery_fees'], 40.0)

        # Cash revenue: 50% of cash delivery fees = 17.5
        self.assertEqual(profit_data['cash_revenue'], 17.5)

        # Point payment costs: 50% of point delivery fees = 20.0
        self.assertEqual(profit_data['point_payment_costs'], 20.0)

        # Company profit: cash_revenue - point_payment_costs = 17.5 - 20.0 = -2.5 (negative!)
        self.assertEqual(profit_data['company_profit'], -2.5)

        # Personnel earnings: 50% of all delivery fees = 37.5
        self.assertEqual(profit_data['personnel_earnings'], 37.5)

    def test_revenue_breakdown_calculation_with_cost_accounting(self):
        """Test comprehensive revenue breakdown with cost accounting"""
        orders = [self.cash_order, self.points_order, self.partial_points_order]

        revenue_data = calculate_revenue_breakdown(orders)

        # Food revenue: 100 + 150 + 80 = 330
        self.assertEqual(revenue_data['food_revenue'], 330.0)

        # Total cash revenue: 130 + 150 + 85 = 365
        self.assertEqual(revenue_data['total_cash_revenue'], 365.0)

        # Total points used: 0 + 25 + 15 = 40
        self.assertEqual(revenue_data['total_points_used'], 40.0)

        # Delivery fees cash: 30 + 0 + 5 = 35
        self.assertEqual(revenue_data['delivery_fees_cash'], 35.0)

        # Delivery fees points: 0 + 25 + 15 = 40
        self.assertEqual(revenue_data['delivery_fees_points'], 40.0)

        # Cash revenue: 50% of cash delivery fees = 17.5
        self.assertEqual(revenue_data['cash_revenue'], 17.5)

        # Point payment costs: 50% of point delivery fees = 20.0
        self.assertEqual(revenue_data['point_payment_costs'], 20.0)

        # Company profit: cash_revenue - point_payment_costs = -2.5 (negative!)
        self.assertEqual(revenue_data['company_profit'], -2.5)

        # Personnel earnings: 50% of all delivery fees = 37.5
        self.assertEqual(revenue_data['personnel_earnings'], 37.5)

    def test_backward_compatibility(self):
        """Test that legacy orders without new fields are handled correctly"""
        breakdown = calculate_payment_breakdown(self.legacy_order)
        
        # Should calculate breakdown from legacy fields
        self.assertEqual(breakdown['cash_amount'], 120.0)  # Only subtotal since points cover delivery
        self.assertEqual(breakdown['points_amount'], 35)
        self.assertEqual(breakdown['delivery_fee_cash'], 0)
        self.assertEqual(breakdown['delivery_fee_points'], 35)
        self.assertEqual(breakdown['subtotal'], 120.0)

    def test_update_order_payment_breakdown(self):
        """Test updating order with payment breakdown fields"""
        order = self.cash_order.copy()
        updated_order = update_order_payment_breakdown(order)
        
        # Should add new fields
        self.assertIn('cash_amount', updated_order)
        self.assertIn('points_amount', updated_order)
        self.assertIn('delivery_fee_cash', updated_order)
        self.assertIn('delivery_fee_points', updated_order)
        
        # Values should be correct
        self.assertEqual(updated_order['cash_amount'], 130.0)
        self.assertEqual(updated_order['points_amount'], 0)
        self.assertEqual(updated_order['delivery_fee_cash'], 30.0)
        self.assertEqual(updated_order['delivery_fee_points'], 0)

    def test_edge_cases(self):
        """Test edge cases and error handling"""
        # Empty order
        empty_order = {}
        breakdown = calculate_payment_breakdown(empty_order)
        self.assertEqual(breakdown['cash_amount'], 0)
        self.assertEqual(breakdown['points_amount'], 0)
        
        # Order with invalid data types
        invalid_order = {
            'subtotal': 'invalid',
            'delivery_fee': None,
            'points_used': 'not_a_number'
        }
        breakdown = calculate_payment_breakdown(invalid_order)
        # Should return safe defaults
        self.assertEqual(breakdown['cash_amount'], 0)
        self.assertEqual(breakdown['points_amount'], 0)

    def test_zero_delivery_fee(self):
        """Test orders with zero delivery fee"""
        zero_fee_order = {
            'subtotal': 50.0,
            'delivery_fee': 0.0,
            'points_used': 0
        }

        breakdown = calculate_payment_breakdown(zero_fee_order)
        self.assertEqual(breakdown['cash_amount'], 50.0)
        self.assertEqual(breakdown['delivery_fee_cash'], 0.0)
        self.assertEqual(breakdown['delivery_fee_points'], 0)

        # Test profit calculations
        profit_data = calculate_company_profit([zero_fee_order])
        self.assertEqual(profit_data['company_profit'], 0.0)
        self.assertEqual(profit_data['personnel_earnings'], 0.0)

    def test_negative_company_profit_scenario(self):
        """Test scenario where company profit goes negative due to point payments"""
        # Order where all delivery fees are paid with points
        all_points_orders = [
            {'subtotal': 100.0, 'delivery_fee': 30.0, 'points_used': 30},
            {'subtotal': 150.0, 'delivery_fee': 25.0, 'points_used': 25},
            {'subtotal': 80.0, 'delivery_fee': 20.0, 'points_used': 20}
        ]

        profit_data = calculate_company_profit(all_points_orders)

        # All delivery fees paid with points: 30 + 25 + 20 = 75
        self.assertEqual(profit_data['points_delivery_fees'], 75.0)
        self.assertEqual(profit_data['cash_delivery_fees'], 0.0)

        # Cash revenue: 0 (no cash delivery fees)
        self.assertEqual(profit_data['cash_revenue'], 0.0)

        # Point payment costs: 50% of 75 = 37.5
        self.assertEqual(profit_data['point_payment_costs'], 37.5)

        # Company profit: 0 - 37.5 = -37.5 (negative!)
        self.assertEqual(profit_data['company_profit'], -37.5)

        # Personnel still get their earnings: 50% of 75 = 37.5
        self.assertEqual(profit_data['personnel_earnings'], 37.5)

    def test_mixed_scenario_with_negative_profit(self):
        """Test mixed scenario where point costs exceed cash revenue"""
        # Scenario: small cash delivery fees, large point delivery fees
        mixed_orders = [
            {'subtotal': 50.0, 'delivery_fee': 10.0, 'points_used': 0},  # Cash: 10
            {'subtotal': 100.0, 'delivery_fee': 50.0, 'points_used': 50},  # Points: 50
            {'subtotal': 75.0, 'delivery_fee': 40.0, 'points_used': 40}   # Points: 40
        ]

        profit_data = calculate_company_profit(mixed_orders)

        # Cash delivery fees: 10, Points delivery fees: 50 + 40 = 90
        self.assertEqual(profit_data['cash_delivery_fees'], 10.0)
        self.assertEqual(profit_data['points_delivery_fees'], 90.0)

        # Cash revenue: 50% of 10 = 5
        self.assertEqual(profit_data['cash_revenue'], 5.0)

        # Point payment costs: 50% of 90 = 45
        self.assertEqual(profit_data['point_payment_costs'], 45.0)

        # Company profit: 5 - 45 = -40 (negative!)
        self.assertEqual(profit_data['company_profit'], -40.0)

        # Personnel earnings: 50% of (10 + 90) = 50
        self.assertEqual(profit_data['personnel_earnings'], 50.0)

    def test_cost_accounting_break_even_scenario(self):
        """Test scenario where cash revenue exactly equals point costs"""
        break_even_orders = [
            {'subtotal': 100.0, 'delivery_fee': 40.0, 'points_used': 0},   # Cash: 40
            {'subtotal': 150.0, 'delivery_fee': 40.0, 'points_used': 40}   # Points: 40
        ]

        profit_data = calculate_company_profit(break_even_orders)

        # Cash revenue: 50% of 40 = 20
        self.assertEqual(profit_data['cash_revenue'], 20.0)

        # Point payment costs: 50% of 40 = 20
        self.assertEqual(profit_data['point_payment_costs'], 20.0)

        # Company profit: 20 - 20 = 0 (break even)
        self.assertEqual(profit_data['company_profit'], 0.0)

        # Personnel earnings: 50% of 80 = 40
        self.assertEqual(profit_data['personnel_earnings'], 40.0)


if __name__ == '__main__':
    unittest.main()
