"""
Menu data for all restaurants.
This file contains the menu items for each restaurant.
"""

import logging

logger = logging.getLogger()

# Default menu items for restaurants that don't have specific menus
default_menu_items = [
    {"id": 1, "name": "Special Erteb", "price": 120},
    {"id": 2, "name": "Normal Erteb", "price": 100},
    {"id": 3, "name": "Special Chechebsa", "price": 230},
    {"id": 4, "name": "Normal Chechebsa", "price": 150},
    {"id": 5, "name": "Full Kita Firfir", "price": 100},
    {"id": 6, "name": "Half Kita Firfir", "price": 60},
    {"id": 7, "name": "Pasta be Sego (Sauce)", "price": 90},
    {"id": 8, "name": "Pasta be Atkelt 🥬", "price": 90},
    {"id": 9, "name": "Pasta be Enkulal 🍳", "price": 100},
    {"id": 10, "name": "Special Sandwich", "price": 100},
    {"id": 11, "name": "Normal Sandwich", "price": 70},
]

# Update the menus dictionary with real menu items
menus = {
    # Bole Area Restaurants
    1: [  # Barech
        {"id": 1, "name": "Full special", "price": 160},
        {"id": 2, "name": "Half Special", "price": 90},
        {"id": 3, "name": "Full Special (Fasting)", "price": 150},
        {"id": 4, "name": "Half Special (Fasting)", "price": 80},
        {"id": 5, "name": "Full Normal Fifir", "price": 100},
        {"id": 6, "name": "Half Normal Firfir", "price": 60},
        {"id": 7, "name": "Pasta be Sego (Sauce)", "price": 90},
        {"id": 8, "name": "Pasta be Atkelt 🥬", "price": 90},
        {"id": 9, "name": "Pasta be Enkulal 🍳", "price": 100},
        {"id": 10, "name": "Soya", "price": 80},
        {"id": 11, "name": "Enkulal Silse ", "price": 80},
        {"id": 12, "name": "Enkulal Firfir", "price": 80},
        {"id": 13, "name": "Shiro", "price": 80},
        {"id": 14, "name": "Timatim Lebleb", "price": 80},
    ],
    2: [  # Mesi
        {"id": 1, "name": "Full special", "price": 160},
        {"id": 2, "name": "Half Special", "price": 90},
        {"id": 3, "name": "Full Special (Fasting)", "price": 150},
        {"id": 4, "name": "Half Special (Fasting)", "price": 90},
        {"id": 5, "name": "Tibs", "price": 220},
        {"id": 6, "name": "Full Tibs Firfir", "price": 180},
        {"id": 7, "name": "Half Tibs Firfir", "price": 100},
        {"id": 8, "name": "Full Normal Fifir", "price": 100},
        {"id": 9, "name": "Half Normal Firfir", "price": 60},
        {"id": 10, "name": "Full Sega Firfir 🥩", "price": 170},
        {"id": 11, "name": "Half Sega Firfir 🥩", "price": 100},
        {"id": 12, "name": "Full Soya Firfir", "price": 130},
        {"id": 13, "name": "Half Soya Firfir", "price": 70},
        {"id": 14, "name": "Aynet", "price": 80},
        {"id": 15, "name": "Pasta be Sega 🥩 ", "price": 100},
        {"id": 16, "name": "Pasta be Enkulal 🍳", "price": 100},
        {"id": 17, "name": "Pasta be Sego (Sauce)", "price": 90},
        {"id": 18, "name": "Pasta be Atkelt 🥬", "price": 90},
        {"id": 19, "name": "Soya be Enkulal 🍳", "price": 100},
        {"id": 20, "name": "Soya", "price": 90},
        {"id": 21, "name": "Special Timati", "price": 90},
        {"id": 22, "name": "Timatim Lebleb", "price": 80},
        {"id": 23, "name": "Timatim Silse", "price": 80},
        {"id": 24, "name": "Enkulal be Sega 🥩", "price": 100},
        {"id": 25, "name": "Enkulal Silse ", "price": 90},
        {"id": 26, "name": "Enkulal Firfir", "price": 90},
        {"id": 27, "name": "Shiro", "price": 80},
        {"id": 28, "name": "Shiro be Kibe", "price": 85},
        {"id": 29, "name": "Dinich 🥔", "price": 80},
        {"id": 30, "name": "Macaroni", "price": 80},
        {"id": 31, "name": "Salad 🥗", "price": 80},
        {"id": 32, "name": "Full Enjera (Extra)", "price": 25},
        {"id": 33, "name": "Half Enjera (Extra)", "price": 15},
        {"id": 34, "name": "Boiled Egg (Extra)", "price": 20},
    ],
    3: [  # Bole Mami
        {"id": 1, "name": "Full special", "price": 160},
        {"id": 2, "name": "Half Special", "price": 90},
        {"id": 3, "name": "Full Special (Fasting)", "price": 150},
        {"id": 4, "name": "Half Special (Fasting)", "price": 80},
        {"id": 5, "name": "Full Special Sega Firfir", "price": 180},
        {"id": 6, "name": "Half Special Sega Firfir", "price": 100},
        {"id": 7, "name": "Full Sega Firfir", "price": 160},
        {"id": 8, "name": "Half Sega Firfir", "price": 90},
        {"id": 9, "name": "Full Normal Fifir", "price": 100},
        {"id": 10, "name": "Half Normal Firfir", "price": 60},
        {"id": 11, "name": "Tibs", "price": 220},
        {"id": 12, "name": "Dinich be Sega 🥔", "price": 90},
        {"id": 13, "name": "Dinich 🥩", "price": 80},
        {"id": 14, "name": "Pasta be Sega 🥩", "price": 100},
        {"id": 15, "name": "Pasta be Sego (Sauce)", "price": 90},
        {"id": 16, "name": "Pasta be Atkelt 🥬", "price": 90},
        {"id": 17, "name": "Pasta be Enkulal 🍳", "price": 100},
        {"id": 18, "name": "Soya", "price": 80},
        {"id": 19, "name": "Timatim Silse ", "price": 80},
        {"id": 20, "name": "Enkulal Firfir", "price": 80},
        {"id": 21, "name": "Shiro", "price": 80},
        {"id": 22, "name": "Timatim Lebleb", "price": 80},
    ],
    4: [  # Helen Megeb-bet
        {"id": 1, "name": "Special Firfir 🍳", "price": 110},
        {"id": 2, "name": "Special Firfir 🥑", "price": 110},
        {"id": 3, "name": "Special Firfir (Ayeb 🧀)", "price": 110},
        {"id": 4, "name": "Sega Firfir 🥩", "price": 160},
        {"id": 5, "name": "Tibs Firfir", "price": 160},
        {"id": 6, "name": "Normal Firfir", "price": 100},
        {"id": 7, "name": "Pasta be Sega 🥩", "price": 130},
        {"id": 8, "name": "Pasta be Enkulal 🍳", "price": 110},
        {"id": 9, "name": "Pasta be Tuna 🐟", "price": 130},
        {"id": 10, "name": "Pasta be Atkilt 🥬", "price": 100},
        {"id": 11, "name": "Pasta be Sego (Sauce)", "price": 100},
        {"id": 12, "name": "Beyaynet", "price": 100},
        {"id": 13, "name": "Timatim Silse", "price": 100},
        {"id": 14, "name": "Timatim lebleb", "price": 100},
        {"id": 15, "name": "Soya", "price": 100},
        {"id": 16, "name": "Enkulal be Sega 🥩", "price": 130},
        {"id": 17, "name": "Enkulal Silse", "price": 110},
        {"id": 18, "name": "Enkulal Firfir", "price": 100},
        {"id": 19, "name": "Tibs", "price": 160},
        {"id": 20, "name": "Key Wot", "price": 160},
        {"id": 21, "name": "Atkilt Tibs 🥬", "price": 100},
        {"id": 22, "name": "Special Enkulal Sandwich 🍳", "price": 120},
        {"id": 24, "name": "Gomen Tibs 🥬", "price": 100},
        {"id": 25, "name": "Gomen be Sega 🥩", "price": 130},
        {"id": 26, "name": "Gomen be Ayeb 🧀", "price": 110},
        {"id": 27, "name": "Gomen Tibs", "price": 100},
        {"id": 28, "name": "Shiro be Gomen 🥬", "price": 110},
        {"id": 29, "name": "Shiro be Ayeb 🧀", "price": 110},
        {"id": 30, "name": "Bozena Shero 🥩", "price": 130},
        {"id": 31, "name": "Shiro Kebta", "price": 130},
        {"id": 32, "name": "Ayeb be Gomen 🥬", "price": 130},
        {"id": 33, "name": "Ayeb be Silse", "price": 110},
        {"id": 34, "name": "Ayeb be Kibe", "price": 110},
        {"id": 35, "name": "Ayeb be Shiro", "price": 110},
    ],
    5: [  # Melat Erteb(PS bet)
        {"id": 1, "name": "Special Erteb", "price": 120},
        {"id": 2, "name": "Normal Erteb", "price": 90},
    ],
    6: [  # Fike Burger
        {"id": 1, "name": "Fike Special Burger + Aluminium", "price": 260},
        {"id": 2, "name": "Double Special Burger + Aluminium", "price": 200},
        {"id": 3, "name": "Single Special Burger + Aluminium", "price": 130},
        {"id": 4, "name": "Cheese Burger + Aluminium", "price": 180},
        {"id": 5, "name": "Normal Burger + Aluminium", "price": 170},
        {"id": 6, "name": "Fike Special Sandwich + Aluminium", "price": 180},
        {"id": 7, "name": "Special Sandwich + Aluminium", "price": 150},
        {"id": 8, "name": "Normal Sandwich + Aluminium", "price": 130},
        {"id": 9, "name": "Vegetable Sandwich + Aluminium", "price": 130},
        {"id": 10, "name": "Fike Special Tuna + Aluminium", "price": 200},
        {"id": 11, "name": "Tuna Special + Aluminium", "price": 170},
        {"id": 12, "name": "Normal Tuna + Aluminium", "price": 150},
        {"id": 13, "name": "French Fries (Chips) + Aluminium", "price": 100},
    ],
    7: [  # Bonali Burger
        {"id": 1, "name": "Bonali Burger + Aluminium", "price": 310},
        {"id": 2, "name": "Jambo Burger + Aluminium", "price": 380},
        {"id": 3, "name": "Double Burger + Aluminium", "price": 290},
        {"id": 4, "name": "Chili Burger + Aluminium", "price": 240},
        {"id": 5, "name": "Cheese Burger + Aluminium", "price": 230},
        {"id": 6, "name": "Family Burger + Aluminium", "price": 360},
        {"id": 7, "name": "Fasting Burger + Aluminium", "price": 160},
        {"id": 8, "name": "Bonali Pizza + Box", "price": 365},
        {"id": 9, "name": "Half Half Pizza + Box", "price": 305},
        {"id": 10, "name": "Chicken Pizza + Box", "price": 285},
        {"id": 11, "name": "Tuna Pizza + Box", "price": 285},
        {"id": 12, "name": "Beef Pizza + Box", "price": 235},
        {"id": 13, "name": "Vegetable Pizza + Box", "price": 235},
        {"id": 14, "name": "Chicken Club Sandwich + Aluminium", "price": 280},
        {"id": 15, "name": "Tuna Club Sandwich + Aluminium", "price": 280},
        {"id": 16, "name": "Beef Club Sandwich + Aluminium", "price": 260},
        {"id": 17, "name": "Bonali Sandwich + Aluminium", "price": 200},
        {"id": 18, "name": "Egg Sandwich + Aluminium", "price": 130},
        {"id": 19, "name": "Vegetable Sandwich + Aluminium", "price": 120},
    ],
    8: [  # Abenezer Burger
        {"id": 1, "name": "Abenezer Pizza + Box", "price": 290},
        {"id": 2, "name": "Tuna Pizza with Cheese + Box", "price": 255},
        {"id": 3, "name": "Tuna Pizza + Box", "price": 230},
        {"id": 4, "name": "Margherita Pizza + Box", "price": 220},
        {"id": 5, "name": "Vegetable Pizza with Cheese + Box", "price": 240},
        {"id": 6, "name": "Vegetable Pizza + Box", "price": 190},
        {"id": 7, "name": "Abenezer Special Burger + Aluminium", "price": 250},
        {"id": 8, "name": "Special Burger + Aluminium", "price": 220},
        {"id": 9, "name": "Cheese Burger + Aluminium", "price": 190},
        {"id": 10, "name": "Normal Burger + Aluminium", "price": 170},
        {"id": 11, "name": "Club Sandwich + Aluminium", "price": 190},
        {"id": 12, "name": "Half Club Sandwich + Aluminium", "price": 110},
        {"id": 13, "name": "Tuna Sandwich + Aluminium", "price": 190},
        {"id": 14, "name": "Special Egg Sandwich + Aluminium", "price": 130},
        {"id": 15, "name": "Egg Sandwich + Aluminium", "price": 105},
        {"id": 16, "name": "French Fries (Chips) + Aluminium", "price": 80},
    ],
    9: [  # Marta Megeb-bet
        {"id": 1, "name": "Full special", "price": 150},
        {"id": 2, "name": "Half Special", "price": 90},
        {"id": 3, "name": "Full Sega Fifir 🥩", "price": 160},
        {"id": 4, "name": "Half Sega Firfir 🥩", "price": 100},
        {"id": 5, "name": "Full Normal Fifir", "price": 90},
        {"id": 6, "name": "Half Normal Firfir", "price": 60},
        {"id": 7, "name": "Pasta be Enkulal 🍳", "price": 80},
        {"id": 8, "name": "Pasta be Sego (Sauce)", "price": 70},
        {"id": 9, "name": "Pasta be Atkelt 🥬", "price": 70},
        {"id": 10, "name": "Enkulal Silse ", "price": 80},
        {"id": 11, "name": "Enkulal Firfir", "price": 80},
        {"id": 12, "name": "Timatim Silse", "price": 60},
        {"id": 13, "name": "Timatim Lebleb", "price": 60},
        {"id": 14, "name": "Soya", "price": 70},
        {"id": 15, "name": "Shiro", "price": 70},
        {"id": 16, "name": "Dinich Wot 🥔", "price": 70},
        {"id": 17, "name": "Gomen 🥬", "price": 70},
    ],
    # Geda Gate Area Restaurants
    10: [  # Food Case
        {"id": 1, "name": "Special Erteb + Box", "price": 150},
        {"id": 2, "name": "Special Felafel + Box", "price": 170},
        {"id": 3, "name": "Normal Erteb(Fasting) + Box", "price": 120},
        {"id": 4, "name": "Normal Felafel(Fasting) + Box", "price": 140},
    ],
    11: [  # Ja Kushena
        {"id": 1, "name": "Ja Burger", "price": 200},
        {"id": 2, "name": "Borrito", "price": 140},
        {"id": 3, "name": "Special Erteb", "price": 110},
        {"id": 4, "name": "Normal Erteb", "price": 90},
    ],
    12: [  # Hawinet Megeb-bet
        {"id": 1, "name": "Hawi-net Special", "price": 500},
        {"id": 2, "name": "Special Afagn", "price": 400},
        {"id": 3, "name": "Full Afagn", "price": 270},
        {"id": 4, "name": "Half Afagn", "price": 180},
        {"id": 5, "name": "Full Tibs Afagn", "price": 300},
        {"id": 6, "name": "Half Tibs Afagn", "price": 210},
        {"id": 7, "name": "Teferesho", "price": 200},
        {"id": 8, "name": "Quanta Firfir", "price": 250},
        {"id": 9, "name": "Special Firfir", "price": 120},
        {"id": 10, "name": "Sega Firfir", "price": 150},
        {"id": 11, "name": "Tibs Firfir", "price": 170},
        {"id": 12, "name": "Normal Firfir", "price": 100},
        {"id": 13, "name": "Pasta be sega 🥩", "price": 140},
        {"id": 14, "name": "Pasta be sego (Sauce)", "price": 90},
        {"id": 15, "name": "Pasta be enkulal 🍳", "price": 120},
        {"id": 16, "name": "Pasta be Atkilt 🥬", "price": 100},
        {"id": 17, "name": "Enkulal be Sega 🥩", "price": 150},
        {"id": 18, "name": "Enkulal Silse", "price": 100},
        {"id": 19, "name": "Enkulal Firfir", "price": 110},
        {"id": 20, "name": "Special Hanchote", "price": 270},
        {"id": 21, "name": "Hanchote", "price": 220},
        {"id": 22, "name": "Dulet", "price": 170},
        {"id": 23, "name": "Tibs", "price": 230},
        {"id": 24, "name": "Tibs Wot", "price": 230},
        {"id": 25, "name": "Minchet", "price": 200},
        {"id": 26, "name": "Ayeb (Cheese)", "price": 150},
        {"id": 27, "name": "Ayeb be Sega", "price": 170},
        {"id": 28, "name": "Ayeb be Gomen 🥬", "price": 150},
        {"id": 29, "name": "Special Ful", "price": 110},
        {"id": 30, "name": "Ful be Avocado", "price": 90},
        {"id": 31, "name": "Ful", "price": 80},
        {"id": 32, "name": "Gomen Tibis 🥬", "price": 100},
        {"id": 33, "name": "Gomen be Sega 🥩", "price": 150},
        {"id": 34, "name": "Timatim lebleb", "price": 100},
    ],
    13: [  # Etu
        {"id": 1, "name": "Full Sega Firfir 🥩", "price": 210},
        {"id": 2, "name": "Half Sega Firfir 🥩", "price": 130},
        {"id": 3, "name": "Full Tibis Firfir", "price": 210},
        {"id": 4, "name": "Half Tibs Firfir", "price": 130},
        {"id": 5, "name": "Full Quanta Firfri", "price": 210},
        {"id": 6, "name": "Half Quanta Firfir", "price": 130},
        {"id": 7, "name": "Full Normal Firfir", "price": 170},
        {"id": 8, "name": "Half Normal Firfir", "price": 110},
        {"id": 9, "name": "Pasta be Sega 🥩", "price": 160},
        {"id": 10, "name": "Macaroni be Sega 🥩", "price": 160},
        {"id": 11, "name": "Ruz be Sega 🥩", "price": 160},
        {"id": 12, "name": "Timatim be Sega 🥩", "price": 160},
        {"id": 13, "name": "Enkulal be Sega 🥩", "price": 170},
        {"id": 14, "name": "Enkulal Silse", "price": 110},
        {"id": 15, "name": "Enkulal Firfir", "price": 110},
        {"id": 16, "name": "Pasta be Sego (Sauce)", "price": 110},
        {"id": 17, "name": "Pasts be Atkilt", "price": 110},
        {"id": 18, "name": "Macaroni be Sego (Sauce)", "price": 110},
        {"id": 19, "name": "Macaroni be Atkilt", "price": 110},
        {"id": 20, "name": "Ruz (Rice)", "price": 110},
        {"id": 21, "name": "Beyaynet", "price": 110},
        {"id": 22, "name": "Tegabino", "price": 160},
        {"id": 23, "name": "Shiro", "price": 110},
        {"id": 24, "name": "Gomen 🥬", "price": 110},
        {"id": 25, "name": "Timatim", "price": 110},
    ],
    14: [  # Beza
        {"id": 1, "name": "Special Erteb ", "price": 110},
        {"id": 2, "name": "Normal Firfir", "price": 100},
        {"id": 3, "name": "Normal Erteb", "price": 90},
        {"id": 4, "name": "Pasta", "price": 100},
        {"id": 5, "name": "Shiro", "price": 100},
    ],
    15: [  # Selam Erteb
        {"id": 1, "name": "Mix Erteb (Soya + Egg)", "price": 110},
        {"id": 2, "name": "Enkulal Erteb", "price": 100},
        {"id": 3, "name": "Soya Erteb", "price": 95},
        {"id": 4, "name": "Erteb be Atkilt", "price": 95},
        {"id": 5, "name": "Normal Erteb", "price": 90},
    ],
    16: [  # Mami-bet
        {"id": 1, "name": "Tibs", "price": 270},
        {"id": 2, "name": "Full Tibs Firfir", "price": 220},
        {"id": 3, "name": "Half Tibs Firfir", "price": 120},
        {"id": 4, "name": "Full Sega Firfir 🥩", "price": 200},
        {"id": 5, "name": "Half Sega Firfir 🥩", "price": 110},
        {"id": 6, "name": "Full Normal Firfir", "price": 170},
        {"id": 7, "name": "Half Normal Firfir", "price": 100},
        {"id": 8, "name": "Enkulal be Sega 🥩", "price": 150},
        {"id": 9, "name": "Enkulal Silse", "price": 110},
        {"id": 10, "name": "Enkulal Firfir", "price": 100},
        {"id": 11, "name": "Pasta be Sega 🥩", "price": 130},
        {"id": 12, "name": "Pasta be Enkulal 🍳", "price": 120},
        {"id": 13, "name": "Pasta be Sego (Sauce)", "price": 100},
        {"id": 14, "name": "Pasta be Atkilt 🥬", "price": 100},
        {"id": 16, "name": "Shiro", "price": 100},
        {"id": 17, "name": "Shiro be Kibe (Butter)", "price": 110},
        {"id": 18, "name": "Bozena Shiro", "price": 150},
        {"id": 19, "name": "Timatim be Sega 🥩", "price": 120},
        {"id": 20, "name": "Timatim be Enkulal 🍳", "price": 120},
        {"id": 21, "name": "Timatim Silse", "price": 100},
        {"id": 22, "name": "Timatim Lebleb", "price": 100},
    ],
    # Flavor Coffee
    19: [
        {"id": 1, "name": "Special Erteb", "price": 120},
        {"id": 2, "name": "Normal Erteb", "price": 100},
        {"id": 3, "name": "Special Chechebsa", "price": 230},
        {"id": 4, "name": "Normal Chechebsa", "price": 150},
        {"id": 5, "name": "Kita Firfir", "price": 100},
        {"id": 6, "name": "Sandwich", "price": 80},
        {"id": 7, "name": "Macchiato ☕", "price": 60},
        {"id": 8, "name": "Espresso ☕", "price": 50},
        {"id": 9, "name": "Americano ☕", "price": 70},
        {"id": 10, "name": "Cappuccino ☕", "price": 80},
        {"id": 11, "name": "Latte ☕", "price": 90},
        {"id": 12, "name": "Mocha ☕", "price": 100},
        {"id": 13, "name": "Hot Chocolate 🍫", "price": 80},
        {"id": 14, "name": "Tea 🍵", "price": 30},
        {"id": 15, "name": "Spris Juice 🥤", "price": 100},
        {"id": 16, "name": "Avocado Juice 🥑", "price": 120},
        {"id": 17, "name": "Mango Juice 🥭", "price": 100},
        {"id": 18, "name": "Strawberry Juice 🍓", "price": 110},
        {"id": 19, "name": "Pineapple Juice 🍍", "price": 100},
        {"id": 20, "name": "Watermelon Juice 🍉", "price": 90},
        {"id": 21, "name": "Banana Juice 🍌", "price": 80},
        {"id": 36, "name": "Iced Tea 🧊", "price": 100},
        {"id": 37, "name": "Iced Latte 🧊", "price": 200},
        {"id": 38, "name": "Iced Mocha 🧊", "price": 250},
    ],
    17: [  # Bontu
        {"id": 1, "name": "Full Firifr", "price": 180},
        {"id": 2, "name": "Half Firfir", "price": 110},
        {"id": 3, "name": "Pasta be Sego", "price": 110},
        {"id": 4, "name": "Pasta be Atkilt", "price": 110},
        {"id": 5, "name": "Shiro", "price": 110},
        {"id": 6, "name": "Beyaynet", "price": 110},
        {"id": 7, "name": "Timatim Lebleb", "price": 110},
        {"id": 8, "name": "Timatim Silse", "price": 110},
        {"id": 9, "name": "Alecha Misir 🟡", "price": 110},
        {"id": 10, "name": "Dinich 🥔", "price": 110},
        {"id": 11, "name": "Gomen 🥬", "price": 110},
        {"id": 12, "name": "Enkulal", "price": 110},
    ],
    18: [  # Ikmah
        {"id": 1, "name": "Special Erteb", "price": 140},
        {"id": 2, "name": "Normal Erteb", "price": 120},
    ],
    # Kereyu Area Restaurants
    19: [  # Flavor Coffee
        {"id": 1, "name": "Combo", "price": 400},
        {"id": 2, "name": "Chechebsa", "price": 180},
        {"id": 3, "name": "Special Chechebsa", "price": 230},
        {"id": 4, "name": "Fetira", "price": 200},
        {"id": 5, "name": "Special Fetira", "price": 250},
        {"id": 6, "name": "Egg Fetira 🍳", "price": 180},
        {"id": 7, "name": "Melewa", "price": 130},
        {"id": 8, "name": "Special Melewa", "price": 200},
        {"id": 9, "name": "Special Salad 🥗", "price": 300},
        {"id": 10, "name": "Salad 🥗", "price": 200},
        {"id": 11, "name": "Pinapple Mojito 🍍", "price": 150},
        {"id": 12, "name": "Strawberry Mojito🍓", "price": 150},
        {"id": 13, "name": "Blue Lagon Mojito 🫐", "price": 150},
        {"id": 14, "name": "Classic Mojito✨💫", "price": 150},
        {"id": 15, "name": "Mint Mojito 🍃", "price": 170},
        {"id": 16, "name": "Special Mojito💫", "price": 170},
        {"id": 17, "name": "Special Shewarma", "price": 500},
        {"id": 18, "name": "Spcial Pizza", "price": 480},
        {"id": 19, "name": "Chicken Pizza 🍗", "price": 500},
        {"id": 20, "name": "Tuna Pizza 🐟", "price": 400},
        {"id": 21, "name": "Margherita Pizza", "price": 400},
        {"id": 22, "name": "Vegitable Pizza 🥬", "price": 280},
        {"id": 23, "name": "Special Burger", "price": 400},
        {"id": 24, "name": "Cheese Burger 🧀", "price": 300},
        {"id": 25, "name": "Beef Burger 🥩", "price": 280},
        {"id": 26, "name": "Chicken Fageta 🍗", "price": 550},
        {"id": 27, "name": "Chicken Burger 🍗", "price": 500},
        {"id": 28, "name": "Club Sandwich", "price": 300},
        {"id": 29, "name": "Special Sandwich", "price": 300},
        {"id": 30, "name": "Tuna Sandwich 🐟", "price": 350},
        {"id": 31, "name": "Spice Sandwich", "price": 200},
        {"id": 32, "name": "Enkulal Sandwich 🍳", "price": 200},
        {"id": 33, "name": "Vegitable Sandwich 🥬", "price": 200},
        {"id": 34, "name": "Chips / French Fries 🍟", "price": 180},
        {"id": 35, "name": "Oreo Shake ", "price": 250},
        {"id": 36, "name": "Iced Tea 🧊", "price": 100},
        {"id": 37, "name": "Iced Latte 🧊", "price": 200},
        {"id": 38, "name": "Iced Mocha 🧊", "price": 200},
        {"id": 39, "name": "Iced Coffee 🧊", "price": 150},
        {"id": 40, "name": "Iced Macchiato 🧊", "price": 200},
        {"id": 41, "name": "Iced Chocholate 🧊", "price": 200},
        {"id": 42, "name": "Iced Strawberry 🧊", "price": 200},
        {"id": 43, "name": "Iced Fasting 🧊", "price": 200},
        {"id": 44, "name": "Flavor Special Cake", "price": 170},
        {"id": 45, "name": "Cheese Cake 🧀", "price": 200},
        {"id": 46, "name": "Opera Cake", "price": 140},
        {"id": 47, "name": "Sture Cake", "price": 140},
        {"id": 48, "name": "Caramel Cake", "price": 140},
        {"id": 49, "name": "Boxegna Cake", "price": 110},
        {"id": 50, "name": "Mini Foni Cake", "price": 110},
        {"id": 51, "name": "Custered Cake", "price": 80},
        {"id": 52, "name": "Teramiso Cake", "price": 100},
        {"id": 53, "name": "Loli pop Cake", "price": 100},
        {"id": 54, "name": "Special Juice", "price": 170},
        {"id": 55, "name": "Avocado Shake", "price": 150},
        {"id": 56, "name": "Papaya Shake", "price": 150},
        {"id": 57, "name": "Strawberry Shake", "price": 170},
        {"id": 58, "name": "Spris Juice", "price": 130},
        {"id": 59, "name": "Papaya Juice", "price": 120},
        {"id": 60, "name": "Avocado Juice", "price": 120},
    ],
    # College Mecheresha Area Restaurants
    20: [  # Mame Erteb
        {"id": 1, "name": "Special Erteb (Large)", "price": 110},
        {"id": 2, "name": "Special Erteb (Small)", "price": 90},
        {"id": 3, "name": "Normal Erteb (Large)", "price": 90},
        {"id": 4, "name": "Normal Erteb (Small)", "price": 70},
    ],
    # Stadium Area Restaurants
    21: [  # Mery Megeb-bet
        {"id": 29, "name": "Special Erteb", "price": 90},
        {"id": 30, "name": "Normal Erteb", "price": 70},
        {"id": 31, "name": "Soya Erteb", "price": 100},
        {"id": 32, "name": "Soya Special Erteb", "price": 130},
        {"id": 2, "name": "Full Tibs Firfir", "price": 230},
        {"id": 3, "name": "Half Tibs Firfir", "price": 130},
        {"id": 4, "name": "Full Sega Firfir", "price": 220},
        {"id": 5, "name": "Half Sega Firfir", "price": 120},
        {"id": 6, "name": "Full Firfir be Kibe", "price": 160},
        {"id": 7, "name": "Half Firfir be Kibe", "price": 90},
        {"id": 8, "name": "Full Normal Firfir", "price": 130},
        {"id": 9, "name": "Half Normal Firfir", "price": 80},
        {"id": 10, "name": "Full Special Firfir", "price": 170},
        {"id": 11, "name": "Half Special Firfir", "price": 100},
        {"id": 1, "name": "Dinich be Sega 🥩", "price": 130},
        {"id": 12, "name": "Enkulal Firfir", "price": 100},
        {"id": 13, "name": "Pasta be Enkulal 🍳", "price": 120},
        {"id": 14, "name": "Pasta be Sego (Sauce)", "price": 100},
        {"id": 15, "name": "Pasta be Atkilt 🥬", "price": 100},
        {"id": 16, "name": "Pasta be Sega 🥩", "price": 140},
        {"id": 17, "name": "Gomen 🥬", "price": 100},
        {"id": 18, "name": "Dinich 🥔", "price": 100},
        {"id": 19, "name": "Shiro", "price": 100},
        {"id": 20, "name": "Bozena Shiro", "price": 140},
        {"id": 21, "name": "Shiro be Kibe", "price": 120},
        {"id": 22, "name": "Beyaynet", "price": 100},
        {"id": 23, "name": "Atkilt 🥬", "price": 100},
        {"id": 24, "name": "Timatim", "price": 100},
        {"id": 25, "name": "Timatim be Avocado 🥑", "price": 120},
        {"id": 26, "name": "Ruze be Atkilt 🥬", "price": 100},
        {"id": 27, "name": "Soya", "price": 100},
        {"id": 28, "name": "Tegabino", "price": 130},
    ],
    22: [  # Melkamu Megeb and Erteb
        {"id": 1, "name": "Special Erteb", "price": 90},
        {"id": 2, "name": "Normal Erteb", "price": 70},
        {"id": 3, "name": "Full Special Firfir", "price": 170},
        {"id": 4, "name": "Half Special Firfir", "price": 100},
        {"id": 5, "name": "Full Sega Firfir", "price": 220},
        {"id": 6, "name": "Half Sega Firfir", "price": 120},
        {"id": 7, "name": "Full Tibs Firfir", "price": 220},
        {"id": 8, "name": "Half Tibs Firfir", "price": 120},
        {"id": 9, "name": "Full Normal Firfir", "price": 130},
        {"id": 10, "name": "Half Normal Firfir", "price": 80},
        {"id": 11, "name": "Full Firfir be Kibe", "price": 150},
        {"id": 12, "name": "Half Firfir be Kibe", "price": 110},
        {"id": 13, "name": "Enkulal be Sega 🥩", "price": 170},
        {"id": 14, "name": "Enkulal Firfir", "price": 100},
        {"id": 15, "name": "Enkulal be Avocado 🥑", "price": 120},
        {"id": 16, "name": "Beyaynet", "price": 100},
        {"id": 17, "name": "Shiro be Kibe", "price": 110},
        {"id": 18, "name": "Shiro", "price": 100},
        {"id": 19, "name": "Tegabino", "price": 130},
        {"id": 20, "name": "Timatim", "price": 100},
        {"id": 21, "name": "Soya", "price": 100},
        {"id": 22, "name": "Pasta be Sega 🥩", "price": 140},
        {"id": 23, "name": "Pasta be Sego (Sauce)", "price": 100},
        {"id": 24, "name": "Pasta be Atkilt 🥬", "price": 100},
        {"id": 25, "name": "Ruz be Sega 🥩", "price": 140},
        {"id": 26, "name": "Ruz be Atkilt 🥬", "price": 100},
        {"id": 27, "name": "Gomen Tibs 🥬", "price": 100},
        {"id": 28, "name": "Selata", "price": 90},
        {"id": 29, "name": "Sandwich", "price": 70},
    ],
    23: [  # Daniel
        {"id": 1, "name": "Special Erteb", "price": 100},
        {"id": 2, "name": "Normal Erteb", "price": 80},
        {"id": 3, "name": "Full Special Firfir", "price": 170},
        {"id": 4, "name": "Half Special Firfir", "price": 100},
        {"id": 5, "name": "Half Normal Firfir", "price": 80},
        {"id": 6, "name": "Full Firfir be Kibe", "price": 160},
        {"id": 7, "name": "Half Firfir be Kibe", "price": 90},
        {"id": 8, "name": "Enkulal Firfir", "price": 90},
        {"id": 9, "name": "Enkulal be Avocado 🥑", "price": 110},
        {"id": 10, "name": "Beyaynet", "price": 90},
        {"id": 11, "name": "Shiro be Kibe", "price": 110},
        {"id": 12, "name": "Shiro", "price": 90},
        {"id": 13, "name": "Tegabino", "price": 120},
        {"id": 14, "name": "Timatim lebleb", "price": 90},
        {"id": 15, "name": "Timatim be Avocado 🥑", "price": 110},
        {"id": 16, "name": "Soya", "price": 100},
        {"id": 17, "name": "Pasta be Enkulal 🍳", "price": 100},
        {"id": 18, "name": "Pasta be Sego (Sauce)", "price": 90},
        {"id": 19, "name": "Pasta be Atkilt 🥬", "price": 90},
        {"id": 20, "name": "Ruz be Enkulal 🍳", "price": 110},
        {"id": 21, "name": "Ruz be Atkilt 🥬", "price": 100},
        {"id": 22, "name": "Ruz", "price": 90},
        {"id": 23, "name": "Selata", "price": 100},
        {"id": 24, "name": "Sandwich", "price": 80},
        {"id": 25, "name": "Special Sandwich", "price": 100},
    ],
    24: [  # Enat Megeb-bet
        {"id": 1, "name": "Erteb", "price": 70},
        {"id": 2, "name": "Erteb be Soya", "price": 130},
        {"id": 3, "name": "Sandwich", "price": 70},
        {"id": 4, "name": "Full Special Firfir", "price": 160},
        {"id": 5, "name": "Half Special Firfir", "price": 100},
        {"id": 6, "name": "Full Tibs Firfir", "price": 230},
        {"id": 7, "name": "Half Tibs Firfir", "price": 130},
        {"id": 8, "name": "Full Sega Firfir 🥩", "price": 220},
        {"id": 9, "name": "Half Sega Firfir 🥩", "price": 120},
        {"id": 10, "name": "Full Firfir be Kibe", "price": 150},
        {"id": 11, "name": "Half Firfir be Kibe", "price": 100},
        {"id": 12, "name": "Full Normal Firfir", "price": 130},
        {"id": 13, "name": "Half Normal Firifr", "price": 80},
        {"id": 14, "name": "Tibs", "price": 270},
        {"id": 15, "name": "Pasta be Sega 🥩", "price": 150},
        {"id": 16, "name": "Pasta be Enkulal 🍳", "price": 150},
        {"id": 17, "name": "Pasta be Sego (Sauce)", "price": 100},
        {"id": 18, "name": "Enkulal be Sega 🥩", "price": 150},
        {"id": 19, "name": "Enkulal Firfir", "price": 100},
        {"id": 20, "name": "Enkulal Silse", "price": 100},
        {"id": 21, "name": "Beyaynet", "price": 100},
        {"id": 22, "name": "Shiro", "price": 100},
        {"id": 23, "name": "Tegabino", "price": 130},
        {"id": 24, "name": "Shiro be Kibe", "price": 120},
        {"id": 25, "name": "Bozena Shero 🥩", "price": 140},
        {"id": 26, "name": "Timatim be Sega 🥩", "price": 150},
        {"id": 27, "name": "Timatim be Enkulal 🍳", "price": 130},
        {"id": 28, "name": "Timatim Silse", "price": 100},
        {"id": 29, "name": "Timatim lebleb", "price": 100},
        {"id": 30, "name": "Timatim be Avocado 🥑", "price": 130},
        {"id": 31, "name": "Soya", "price": 100},
        {"id": 32, "name": "Dinich", "price": 100},
        {"id": 33, "name": "Dinich be Siga 🥩", "price": 150},
        {"id": 34, "name": "Atikilt 🥬", "price": 100},
        {"id": 35, "name": "Gomen 🥬", "price": 100},
        {"id": 36, "name": "Ruz be Atikilt 🥬", "price": 100},
    ],
    25: [  # Mery Megeb-bet
        {"id": 29, "name": "Special Erteb", "price": 90},
        {"id": 30, "name": "Normal Erteb", "price": 70},
        {"id": 31, "name": "Soya Erteb", "price": 100},
        {"id": 32, "name": "Soya Special Erteb", "price": 130},
        {"id": 2, "name": "Full Tibs Firfir", "price": 230},
        {"id": 3, "name": "Half Tibs Firfir", "price": 130},
        {"id": 4, "name": "Full Sega Firfir", "price": 220},
        {"id": 5, "name": "Half Sega Firfir", "price": 120},
        {"id": 6, "name": "Full Firfir be Kibe", "price": 160},
        {"id": 7, "name": "Half Firfir be Kibe", "price": 90},
        {"id": 8, "name": "Full Normal Firfir", "price": 130},
        {"id": 9, "name": "Half Normal Firfir", "price": 80},
        {"id": 10, "name": "Full Special Firfir", "price": 170},
        {"id": 11, "name": "Half Special Firfir", "price": 100},
        {"id": 1, "name": "Dinich be Sega 🥩", "price": 130},
        {"id": 12, "name": "Enkulal Firfir", "price": 100},
        {"id": 13, "name": "Pasta be Enkulal 🍳", "price": 120},
        {"id": 14, "name": "Pasta be Sego (Sauce)", "price": 100},
        {"id": 15, "name": "Pasta be Atkilt 🥬", "price": 100},
        {"id": 16, "name": "Pasta be Sega 🥩", "price": 140},
        {"id": 17, "name": "Gomen 🥬", "price": 100},
        {"id": 18, "name": "Dinich 🥔", "price": 100},
        {"id": 19, "name": "Shiro", "price": 100},
        {"id": 20, "name": "Bozena Shiro", "price": 140},
        {"id": 21, "name": "Shiro be Kibe", "price": 120},
        {"id": 22, "name": "Beyaynet", "price": 100},
        {"id": 23, "name": "Atkilt 🥬", "price": 100},
        {"id": 24, "name": "Timatim", "price": 100},
        {"id": 25, "name": "Timatim be Avocado 🥑", "price": 120},
        {"id": 26, "name": "Ruze be Atkilt 🥬", "price": 100},
        {"id": 27, "name": "Soya", "price": 100},
        {"id": 28, "name": "Tegabino", "price": 130},
    ],
}

# Additional code to ensure menu for restaurant ID 15 is definitely available
# This will ensure Selam Erteb (ID 15) has at least default menu items even if not already in the menus dict

# Test if menu for restaurant ID 15 exists and create it if not
if 15 not in menus:
    logger.warning(
        "Menu for restaurant ID 15 (Selam Erteb) not found in predefined menus. Creating default menu."
    )
    menus[15] = [
        {"id": 1, "name": "Mix Erteb (Soya + Egg)", "price": 110},
        {"id": 2, "name": "Enkulal Erteb", "price": 100},
        {"id": 3, "name": "Soya Erteb", "price": 95},
        {"id": 4, "name": "Erteb be Atkilt", "price": 95},
        {"id": 5, "name": "Normal Erteb", "price": 90},
    ]
else:
    logger.debug(f"Menu for restaurant ID 15 exists with {len(menus[15])} items")

# Ensure all menu values are lists to avoid type errors
for key in list(menus.keys()):
    if not isinstance(menus[key], list):
        logger.error(f"Menu for restaurant ID {key} is not a list, fixing...")
        menus[key] = default_menu_items.copy()


def initialize_menus():
    """Add default menus for all restaurants that don't have one defined"""
    # Import here to avoid circular import
    from src.config import restaurants as restaurant_config

    logger.info("Starting menu initialization...")
    logger.debug(f"Predefined menus type: {type(menus)}")
    logger.debug(f"Predefined menus keys: {list(menus.keys())}")

    # Get all restaurant IDs from all areas
    all_restaurant_ids = set()
    for area_restaurants in restaurant_config.values():
        for restaurant_id in area_restaurants.keys():
            # Ensure all restaurant IDs are integers
            try:
                restaurant_id_int = int(restaurant_id)
                all_restaurant_ids.add(restaurant_id_int)
            except (ValueError, TypeError):
                logger.error(f"Invalid restaurant ID in config: {restaurant_id}")
                continue

    logger.info(f"Found {len(all_restaurant_ids)} restaurants in config")

    # For any restaurant without a menu, add a default menu
    existing_menu_count = 0
    new_menu_count = 0
    for restaurant_id in all_restaurant_ids:
        if restaurant_id in menus:
            logger.debug(
                f"Menu already exists for restaurant ID {restaurant_id} with {len(menus[restaurant_id])} items"
            )
            existing_menu_count += 1
        else:
            menus[restaurant_id] = default_menu_items.copy()
            logger.info(
                f"Created default menu for restaurant ID {restaurant_id} with {len(default_menu_items)} items"
            )
            new_menu_count += 1

    # Validate that all menu entries have the required fields
    valid_menus = 0
    invalid_menus = 0
    for restaurant_id, menu_items in menus.items():
        menu_valid = True
        for i, item in enumerate(menu_items):
            if not all(key in item for key in ["id", "name", "price"]):
                logger.warning(
                    f"Menu item {i} for restaurant {restaurant_id} is missing required fields"
                )
                menu_valid = False

        if menu_valid:
            valid_menus += 1
        else:
            invalid_menus += 1

    logger.info(f"Menu initialization summary:")
    logger.info(f"- Total menus: {len(menus)}")
    logger.info(f"- Existing menus: {existing_menu_count}")
    logger.info(f"- New default menus: {new_menu_count}")
    logger.info(f"- Valid menus: {valid_menus}")
    logger.info(f"- Invalid menus: {invalid_menus}")
    print("Successfully initialized menus!")


# The initialization will be called from main.py to avoid circular imports
