"""
Order Tracking Bot for Wiz Aroma Delivery System
Tracks financially confirmed orders and their delivery status.
Access restricted to authorized Telegram IDs.
"""

import telebot
from telebot import types
import datetime
from typing import Dict, List, Any, Optional
import logging

from src.config import (
    ORDER_TRACK_BOT_TOKEN,
    ORDER_TRACK_BOT_AUTHORIZED_IDS,
    logger
)
from src.data_storage import (
    load_pending_admin_reviews,
    load_awaiting_receipt,
    load_order_status,
    load_order_history,
    get_restaurant_by_id,
    get_area_by_id
)
from src.firebase_db import get_data, set_data, delete_data
from src.data_models import (
    pending_admin_reviews,
    awaiting_receipt,
    order_status,
    user_order_history,
    delivery_personnel_assignments
)
from src.utils.delivery_personnel_utils import get_delivery_assignment_by_order

# Initialize the order tracking bot
order_track_bot = telebot.TeleBot(ORDER_TRACK_BOT_TOKEN)

def is_authorized(user_id: int) -> bool:
    """Check if user is authorized to access the order tracking bot"""
    return user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS

def access_denied_message(message):
    """Send access denied message to unauthorized users"""
    order_track_bot.reply_to(
        message,
        "🚫 Access Denied\n\nYou are not authorized to use this order tracking system."
    )

@order_track_bot.message_handler(commands=['start'])
def start_command(message):
    """Handle /start command"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    welcome_text = """
🔍 **Order Tracking System**
Welcome to the Wiz Aroma Order Tracking Bot!

**Available Commands:**
📋 /orders - View all financially confirmed orders
🔍 /track [order_number] - Track specific order
📊 /status - View order status summary
📈 /stats - View delivery statistics
🔄 /refresh - Refresh order data

This system tracks orders that have been financially confirmed and shows their delivery status.
    """
    
    order_track_bot.reply_to(message, welcome_text, parse_mode='Markdown')

@order_track_bot.message_handler(commands=['orders'])
def view_orders_command(message):
    """View all financially confirmed orders"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    try:
        # Load confirmed orders from Firebase
        confirmed_orders = get_data("confirmed_orders") or {}

        if not confirmed_orders:
            order_track_bot.reply_to(
                message,
                "📭 No financially confirmed orders found."
            )
            return
        
        # Create order list
        order_list = []
        for order_number, order_data in confirmed_orders.items():
            # Get order status
            status = order_data.get('status', 'CONFIRMED')
            
            # Get restaurant info
            restaurant_id = order_data.get('restaurant_id')
            restaurant = get_restaurant_by_id(restaurant_id)
            restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
            
            # Get delivery assignment status
            assignment = get_delivery_assignment_by_order(order_number)
            delivery_status = "Not Assigned"
            if assignment:
                delivery_status = assignment.get('status', 'Unknown').title()
            
            order_info = f"""
📋 **Order #{order_number}**
🏪 Restaurant: {restaurant_name}
📱 Phone: {order_data.get('phone_number', 'N/A')}
📍 Delivery: {order_data.get('delivery_location', 'N/A')}
💰 Total: {order_data.get('subtotal', 0)} birr
📊 Status: {status}
🚚 Delivery: {delivery_status}
⏰ Created: {order_data.get('created_at', 'N/A')}
            """
            order_list.append(order_info.strip())
        
        # Split into chunks if too long
        if len(order_list) <= 5:
            response = "🔍 **Financially Confirmed Orders:**\n\n" + "\n\n".join(order_list)
            order_track_bot.reply_to(message, response, parse_mode='Markdown')
        else:
            # Send in chunks
            for i in range(0, len(order_list), 5):
                chunk = order_list[i:i+5]
                chunk_text = f"🔍 **Orders ({i+1}-{min(i+5, len(order_list))} of {len(order_list)}):**\n\n" + "\n\n".join(chunk)
                order_track_bot.send_message(message.chat.id, chunk_text, parse_mode='Markdown')
                
    except Exception as e:
        logger.error(f"Error in view_orders_command: {e}")
        order_track_bot.reply_to(
            message,
            "❌ Error retrieving orders. Please try again later."
        )

@order_track_bot.message_handler(commands=['track'])
def track_order_command(message):
    """Track a specific order by order number"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    try:
        # Extract order number from command
        command_parts = message.text.split()
        if len(command_parts) < 2:
            order_track_bot.reply_to(
                message,
                "❌ Please provide an order number.\nUsage: /track ORDER_NUMBER"
            )
            return
        
        order_number = command_parts[1]
        
        # Load order data from confirmed orders collection
        confirmed_orders = get_data("confirmed_orders") or {}

        if order_number not in confirmed_orders:
            order_track_bot.reply_to(
                message,
                f"❌ Order #{order_number} not found in confirmed orders."
            )
            return

        order_data = confirmed_orders[order_number]
        user_id_str = str(order_data.get('user_id', ''))
        status = order_statuses.get(user_id_str, 'Unknown')
        
        # Get restaurant info
        restaurant_id = order_data.get('restaurant_id')
        restaurant = get_restaurant_by_id(restaurant_id)
        restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"
        
        # Get delivery assignment details
        assignment = get_delivery_assignment_by_order(order_number)
        delivery_info = "🚚 **Delivery Status:** Not Assigned"
        
        if assignment:
            from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
            personnel = get_delivery_personnel_by_id(assignment.get('personnel_id'))
            personnel_name = personnel.name if personnel else "Unknown Driver"
            
            delivery_info = f"""
🚚 **Delivery Status:** {assignment.get('status', 'Unknown').title()}
👤 **Driver:** {personnel_name}
📞 **Driver Phone:** {personnel.phone_number if personnel else 'N/A'}
⏰ **Assigned:** {assignment.get('assigned_at', 'N/A')}
🕐 **Est. Pickup:** {assignment.get('estimated_pickup_time', 'N/A')}
🕑 **Est. Delivery:** {assignment.get('estimated_delivery_time', 'N/A')}
            """
            
            if assignment.get('actual_pickup_time'):
                delivery_info += f"\n✅ **Picked Up:** {assignment.get('actual_pickup_time')}"
            if assignment.get('actual_delivery_time'):
                delivery_info += f"\n🎯 **Delivered:** {assignment.get('actual_delivery_time')}"
        
        # Format order items
        items_text = ""
        if order_data.get('items'):
            items_text = "\n📋 **ORDER ITEMS:**\n"
            for item in order_data['items']:
                items_text += f"• {item.get('name', 'Unknown')} (x{item.get('quantity', 1)}) - {item.get('price', 0)} birr\n"
        
        detailed_info = f"""
🔍 **Order Tracking - #{order_number}**

📱 **Phone:** {order_data.get('phone_number', 'N/A')}
🏪 **Restaurant:** {restaurant_name}
📍 **Delivery to:** {order_data.get('delivery_location', 'N/A')}
{items_text}
💰 **Subtotal:** {order_data.get('subtotal', 0)} birr
📊 **Order Status:** {status}
⏰ **Created:** {order_data.get('created_at', 'N/A')}

{delivery_info}
        """
        
        order_track_bot.reply_to(message, detailed_info.strip(), parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Error in track_order_command: {e}")
        order_track_bot.reply_to(
            message,
            "❌ Error tracking order. Please try again later."
        )

@order_track_bot.message_handler(commands=['status'])
def status_summary_command(message):
    """View order status summary"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    try:
        # Load data
        awaiting_orders = load_awaiting_receipt()
        order_statuses = load_order_status()
        
        # Count orders by status
        status_counts = {}
        delivery_status_counts = {}
        
        for order_number, order_data in awaiting_orders.items():
            user_id_str = str(order_data.get('user_id', ''))
            status = order_statuses.get(user_id_str, 'Unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
            
            # Count delivery statuses
            assignment = get_delivery_assignment_by_order(order_number)
            if assignment:
                delivery_status = assignment.get('status', 'unknown')
                delivery_status_counts[delivery_status] = delivery_status_counts.get(delivery_status, 0) + 1
            else:
                delivery_status_counts['not_assigned'] = delivery_status_counts.get('not_assigned', 0) + 1
        
        summary = f"""
📊 **Order Status Summary**

**Total Financially Confirmed Orders:** {len(awaiting_orders)}

**Order Statuses:**
"""
        
        for status, count in status_counts.items():
            summary += f"• {status}: {count}\n"
        
        summary += "\n**Delivery Statuses:**\n"
        for delivery_status, count in delivery_status_counts.items():
            summary += f"• {delivery_status.replace('_', ' ').title()}: {count}\n"
        
        summary += f"\n⏰ **Last Updated:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        order_track_bot.reply_to(message, summary, parse_mode='Markdown')
        
    except Exception as e:
        logger.error(f"Error in status_summary_command: {e}")
        order_track_bot.reply_to(
            message,
            "❌ Error generating status summary. Please try again later."
        )

@order_track_bot.message_handler(commands=['refresh'])
def refresh_data_command(message):
    """Refresh order data from Firebase"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    try:
        # Reload data from Firebase
        load_awaiting_receipt()
        load_order_status()
        load_pending_admin_reviews()
        
        order_track_bot.reply_to(
            message,
            "✅ Order data refreshed successfully from Firebase."
        )
        
    except Exception as e:
        logger.error(f"Error in refresh_data_command: {e}")
        order_track_bot.reply_to(
            message,
            "❌ Error refreshing data. Please try again later."
        )

@order_track_bot.message_handler(func=lambda message: True)
def handle_unknown_command(message):
    """Handle unknown commands"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        access_denied_message(message)
        return
    
    help_text = """
❓ **Unknown Command**

**Available Commands:**
📋 /orders - View all financially confirmed orders
🔍 /track [order_number] - Track specific order
📊 /status - View order status summary
🔄 /refresh - Refresh order data

Example: `/track ORD_20241230_001`
    """
    
    order_track_bot.reply_to(message, help_text, parse_mode='Markdown')

def send_order_status_update(order_number: str, new_status: str, additional_info: str = "", replace_previous: bool = True):
    """Send order status update to all authorized tracking users with single message editing"""
    try:
        # Get order data
        confirmed_orders = get_data("confirmed_orders") or {}
        if order_number not in confirmed_orders:
            logger.warning(f"Order {order_number} not found for status update")
            return

        order_data = confirmed_orders[order_number]

        # Get restaurant info
        restaurant_id = order_data.get('restaurant_id')
        restaurant = get_restaurant_by_id(restaurant_id)
        restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"

        # Get delivery personnel info if assigned (enhanced with debugging)
        delivery_personnel_info = ""
        assigned_to = order_data.get('assigned_to')
        logger.info(f"📋 Order {order_number} - assigned_to field: {assigned_to}")

        if assigned_to:
            try:
                from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
                personnel_data = get_delivery_personnel_by_id(assigned_to)
                logger.info(f"📋 Retrieved personnel data for {assigned_to}: {personnel_data}")

                if personnel_data:
                    personnel_name = personnel_data.get('name', 'Unknown')
                    personnel_phone = personnel_data.get('phone', 'N/A')
                    delivery_personnel_info = f"\n👤 **Delivery Personnel:** {personnel_name} ({personnel_phone})"
                    logger.info(f"✅ Delivery personnel info formatted: {delivery_personnel_info.strip()}")
                else:
                    logger.warning(f"⚠️ No personnel data found for ID: {assigned_to}")
            except Exception as e:
                logger.error(f"❌ Error getting delivery personnel info for {assigned_to}: {e}")
        else:
            logger.info(f"📋 Order {order_number} - No delivery personnel assigned yet")

        # Create status update message with comprehensive order details
        # Position delivery personnel info prominently after status when assigned
        status_section = f"📊 **Current Status:** {new_status}"
        if delivery_personnel_info:
            status_section += delivery_personnel_info

        # Use the complete format function for consistency
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        additional_timing = f"• Status Updated: {current_time}\n"

        # Format status info with delivery personnel if assigned
        status_info = new_status
        if delivery_personnel_info:
            status_info += f" - {delivery_personnel_info.strip().replace('👤 **Delivery Personnel:** ', 'Assigned to ')}"
        if additional_info:
            status_info += f" - {additional_info}"

        # Use the complete format function
        status_message = format_complete_order_details(
            order_number,
            order_data,
            "ORDER STATUS UPDATE",
            status_info,
            additional_timing
        )

        # Determine if manual confirmation button should be shown
        markup = None
        if should_show_manual_confirmation_button(order_data):
            markup = create_manual_confirmation_button(order_number)

        # Always try to update existing message first (single message system)
        previous_message_id = order_data.get('tracking_message_id')

        if previous_message_id and replace_previous:
            # Update existing message
            updated_any = False
            for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
                try:
                    order_track_bot.edit_message_text(
                        status_message,
                        user_id,
                        previous_message_id,
                        reply_markup=markup,
                        parse_mode='Markdown'
                    )
                    logger.info(f"✅ Updated tracking message for order {order_number} to user {user_id}")
                    updated_any = True
                except Exception as e:
                    logger.warning(f"⚠️ Failed to edit tracking message for user {user_id}: {e}")
                    # If edit fails, try sending new message
                    try:
                        sent_message = order_track_bot.send_message(
                            user_id,
                            status_message,
                            reply_markup=markup,
                            parse_mode='Markdown'
                        )
                        # Update message ID for this user
                        order_data['tracking_message_id'] = sent_message.message_id
                        set_data(f"confirmed_orders/{order_number}", order_data)
                        logger.info(f"📤 Sent new tracking message for order {order_number} to user {user_id}")
                        updated_any = True
                    except Exception as send_e:
                        logger.error(f"❌ Failed to send new tracking message to user {user_id}: {send_e}")

            if not updated_any:
                logger.error(f"❌ Failed to update tracking message for order {order_number} to any user")
        else:
            # Send initial message (no previous message or replace_previous=False)
            for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
                try:
                    sent_message = order_track_bot.send_message(
                        user_id,
                        status_message,
                        reply_markup=markup,
                        parse_mode='Markdown'
                    )
                    # Store message ID for future updates
                    order_data['tracking_message_id'] = sent_message.message_id
                    set_data(f"confirmed_orders/{order_number}", order_data)
                    logger.info(f"📤 Sent initial tracking message for order {order_number} to user {user_id}")
                except Exception as e:
                    logger.error(f"❌ Failed to send initial tracking message to user {user_id}: {e}")

    except Exception as e:
        logger.error(f"❌ Error sending order status update: {e}")

def send_detailed_order_notification(order_number: str, restaurant_name: str, order_data: dict, items_text: str, approved_at: str):
    """Send detailed order notification to order tracking bot with full administrative details"""
    try:
        # Use the complete format function for consistency
        detailed_message = format_complete_order_details(
            order_number,
            order_data,
            "NEW ORDER AVAILABLE",
            "Payment Approved - Broadcasting to delivery personnel",
            ""  # No additional timing for initial notification
        )

        # Send to all authorized tracking users
        for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
            try:
                sent_message = order_track_bot.send_message(user_id, detailed_message, parse_mode='Markdown')
                # Store message ID for future updates
                order_data['tracking_message_id'] = sent_message.message_id
                set_data(f"confirmed_orders/{order_number}", order_data)
                logger.info(f"📤 Sent detailed order notification for order {order_number} to tracking user {user_id}")
            except Exception as e:
                logger.error(f"❌ Failed to send detailed order notification to tracking user {user_id}: {e}")

    except Exception as e:
        logger.error(f"❌ Error sending detailed order notification: {e}")


def should_show_manual_confirmation_button(order_data: dict) -> bool:
    """Determine if admin confirmation button should be shown"""
    try:
        delivery_status = order_data.get('delivery_status', '')
        order_status = order_data.get('status', '')

        # Show admin confirmation button only when:
        # 1. Delivery personnel has marked order as completed
        # 2. Customer has not yet confirmed receipt
        # 3. Order is not already admin confirmed
        # 4. Order is not cancelled or in error state

        # Check if delivery is completed by personnel
        if delivery_status != 'completed':
            return False

        # Check if already confirmed (customer or admin)
        if delivery_status == 'customer_confirmed' or order_status == 'CUSTOMER_CONFIRMED':
            return False

        # Check if order is cancelled or in error state
        if delivery_status in ['cancelled', 'error']:
            return False

        # Check analytics status to ensure it's pending confirmation
        analytics_status = order_data.get('analytics_status', '')
        if analytics_status in ['completed_pending_confirmation', 'resolved_pending_confirmation']:
            return True

        # Fallback: show if delivery is completed but not confirmed
        return True

    except Exception as e:
        logger.error(f"Error determining admin button visibility: {e}")
        return False


def create_manual_confirmation_button(order_number: str) -> types.InlineKeyboardMarkup:
    """Create inline keyboard with admin confirmation button"""
    markup = types.InlineKeyboardMarkup()
    confirm_btn = types.InlineKeyboardButton(
        "🔧 Admin Confirm Order",
        callback_data=f"admin_confirm_{order_number}"
    )
    contact_btn = types.InlineKeyboardButton(
        "📞 Contact Customer",
        callback_data=f"contact_customer_{order_number}"
    )
    markup.row(confirm_btn)
    markup.row(contact_btn)
    return markup


def format_complete_order_details(order_number: str, order_data: dict, status_title: str, status_info: str, additional_timing: str = ""):
    """Format complete order details for order tracking bot with consistent structure"""
    try:
        # Get restaurant information
        restaurant_id = order_data.get('restaurant_id')
        restaurant = get_restaurant_by_id(restaurant_id) if restaurant_id else None
        restaurant_name = restaurant.get('name', 'Unknown Restaurant') if restaurant else 'Unknown Restaurant'

        # Get area information
        restaurant_area = "Unknown Area"
        if restaurant and restaurant.get('area_id'):
            area = get_area_by_id(restaurant['area_id'])
            if area and area.get('name'):
                restaurant_area = area['name']

        # Format delivery location
        delivery_location = order_data.get('delivery_gate', 'Location not specified')
        if not delivery_location or delivery_location == 'N/A':
            delivery_location = order_data.get('delivery_location', 'Location not specified')

        # Format items
        items_text = ""
        if order_data.get('items'):
            items_text = "📋 **Order Items:**\n"
            for item in order_data['items']:
                item_name = item.get('name', 'Unknown Item')
                item_quantity = item.get('quantity', 1)
                item_price = item.get('price', 0)
                total_price = item_price * item_quantity
                items_text += f"• {item_name} x{item_quantity} - {total_price} Birr\n"
            items_text += "\n"

        # Calculate totals
        subtotal = order_data.get('subtotal', 0)
        delivery_fee = order_data.get('delivery_fee', 0)
        total_amount = subtotal + delivery_fee

        # Format timing information
        timing_text = "⏰ **Timing:**\n"
        if order_data.get('created_at'):
            timing_text += f"• Order Placed: {order_data['created_at']}\n"
        if order_data.get('approved_at') or order_data.get('confirmed_at'):
            confirmed_time = order_data.get('approved_at') or order_data.get('confirmed_at')
            timing_text += f"• Payment Confirmed: {confirmed_time}\n"
        if additional_timing:
            timing_text += additional_timing

        # Create complete message
        message = f"""🚚 **{status_title}**

📋 **Order #{order_number}**
🏪 **Restaurant**: {restaurant_name}
🏢 **Restaurant Area**: {restaurant_area}

👤 **Customer Details:**
📱 **Phone**: {order_data.get('phone_number', 'N/A')}
👤 **Name**: {order_data.get('delivery_name', 'N/A')}
📍 **Delivery Address**: {delivery_location}
🚪 **Gate**: {order_data.get('delivery_gate', 'N/A')}

{items_text}💰 **Order Summary:**
• Subtotal: {subtotal} Birr
• Delivery Fee: {delivery_fee} Birr
• **Total Amount**: {total_amount} Birr

{timing_text}
📊 **Status**: {status_info}"""

        return message

    except Exception as e:
        logger.error(f"Error formatting complete order details: {e}")
        return f"🚚 **{status_title}**\n\n📋 **Order #{order_number}**\n📊 **Status**: {status_info}"


def notify_delivery_assignment(order_number: str, personnel_name: str, personnel_phone: str):
    """Notify when order is assigned to delivery personnel"""
    try:
        # Get complete order data
        order_data = get_data(f"confirmed_orders/{order_number}")
        if not order_data:
            logger.error(f"Order data not found for {order_number}")
            return

        # Add assignment timing
        assignment_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        additional_timing = f"• Assigned to Delivery: {assignment_time}\n"

        # Format complete message
        message = format_complete_order_details(
            order_number,
            order_data,
            "ORDER ASSIGNED TO DELIVERY PERSONNEL",
            f"Assigned to {personnel_name} ({personnel_phone})",
            additional_timing
        )

        # Send to all authorized tracking users
        for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
            try:
                # Try to update existing message first
                if order_data.get('tracking_message_id'):
                    try:
                        order_track_bot.edit_message_text(
                            message,
                            user_id,
                            order_data['tracking_message_id'],
                            parse_mode='Markdown'
                        )
                        logger.info(f"📤 Updated tracking message for order {order_number} assignment")
                        continue
                    except Exception as edit_error:
                        logger.warning(f"Failed to edit tracking message, sending new one: {edit_error}")

                # Send new message if edit failed
                sent_message = order_track_bot.send_message(user_id, message, parse_mode='Markdown')
                order_data['tracking_message_id'] = sent_message.message_id
                set_data(f"confirmed_orders/{order_number}", order_data)
                logger.info(f"📤 Sent new tracking message for order {order_number} assignment")

            except Exception as e:
                logger.error(f"❌ Failed to send assignment notification to tracking user {user_id}: {e}")

    except Exception as e:
        logger.error(f"❌ Error in notify_delivery_assignment: {e}")

def notify_delivery_accepted(order_number: str, personnel_name: str):
    """Notify when delivery personnel accepts the order"""
    try:
        # Get complete order data
        order_data = get_data(f"confirmed_orders/{order_number}")
        if not order_data:
            logger.error(f"Order data not found for {order_number}")
            return

        # Add acceptance timing
        acceptance_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        additional_timing = f"• Accepted by Driver: {acceptance_time}\n"

        # Format complete message
        message = format_complete_order_details(
            order_number,
            order_data,
            "ORDER ACCEPTED BY DELIVERY PERSONNEL",
            f"Driver {personnel_name} has accepted the order",
            additional_timing
        )

        # Send to all authorized tracking users
        for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
            try:
                # Try to update existing message first
                if order_data.get('tracking_message_id'):
                    try:
                        order_track_bot.edit_message_text(
                            message,
                            user_id,
                            order_data['tracking_message_id'],
                            parse_mode='Markdown'
                        )
                        logger.info(f"📤 Updated tracking message for order {order_number} acceptance")
                        continue
                    except Exception as edit_error:
                        logger.warning(f"Failed to edit tracking message, sending new one: {edit_error}")

                # Send new message if edit failed
                sent_message = order_track_bot.send_message(user_id, message, parse_mode='Markdown')
                order_data['tracking_message_id'] = sent_message.message_id
                set_data(f"confirmed_orders/{order_number}", order_data)
                logger.info(f"📤 Sent new tracking message for order {order_number} acceptance")

            except Exception as e:
                logger.error(f"❌ Failed to send acceptance notification to tracking user {user_id}: {e}")

    except Exception as e:
        logger.error(f"❌ Error in notify_delivery_accepted: {e}")

def notify_delivery_completed(order_number: str, personnel_name: str):
    """Notify when delivery personnel marks order as completed"""
    try:
        # Get complete order data
        order_data = get_data(f"confirmed_orders/{order_number}")
        if not order_data:
            logger.error(f"Order data not found for {order_number}")
            return

        # Add completion timing
        completion_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        additional_timing = f"• Completed by Driver: {completion_time}\n"

        # Format complete message
        message = format_complete_order_details(
            order_number,
            order_data,
            "DELIVERY COMPLETED",
            f"Driver {personnel_name} has marked the order as delivered. Customer confirmation request sent.",
            additional_timing
        )

        # Create inline keyboard with manual confirmation button if needed
        markup = None
        if should_show_manual_confirmation_button(order_data):
            markup = create_manual_confirmation_button(order_number)

        # Send to all authorized tracking users
        for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
            try:
                # Try to update existing message first
                if order_data.get('tracking_message_id'):
                    try:
                        order_track_bot.edit_message_text(
                            message,
                            user_id,
                            order_data['tracking_message_id'],
                            reply_markup=markup,
                            parse_mode='Markdown'
                        )
                        logger.info(f"📤 Updated tracking message for order {order_number} completion with buttons")
                        continue
                    except Exception as edit_error:
                        logger.warning(f"Failed to edit tracking message, sending new one: {edit_error}")

                # Send new message if edit failed
                sent_message = order_track_bot.send_message(
                    user_id,
                    message,
                    reply_markup=markup,
                    parse_mode='Markdown'
                )
                order_data['tracking_message_id'] = sent_message.message_id
                set_data(f"confirmed_orders/{order_number}", order_data)
                logger.info(f"📤 Sent new tracking message for order {order_number} completion with buttons")

            except Exception as e:
                logger.error(f"❌ Failed to send completion notification to tracking user {user_id}: {e}")

    except Exception as e:
        logger.error(f"❌ Error in notify_delivery_completed: {e}")

    # Send confirmation button to customer
    send_customer_confirmation_request(order_number)

def send_customer_confirmation_request(order_number: str):
    """Edit the original payment verification message to include delivery completion and confirmation request"""
    try:
        logger.info(f"🔔 Starting customer confirmation request for order {order_number}")

        # Get order data
        confirmed_orders = get_data("confirmed_orders") or {}
        if order_number not in confirmed_orders:
            logger.warning(f"Order {order_number} not found for customer confirmation")
            return

        order_data = confirmed_orders[order_number]

        # Extract customer user ID from order data or order number
        customer_user_id = order_data.get('user_id')

        # If user_id not in order data, extract from order number format: {user_id}_{date}_{sequence}
        if not customer_user_id:
            try:
                customer_user_id = order_number.split('_')[0]
                logger.info(f"🔍 Extracted customer user ID from order number: {customer_user_id}")
            except (IndexError, ValueError):
                logger.error(f"❌ Could not extract user ID from order number: {order_number}")
                return

        if not customer_user_id:
            logger.warning(f"No customer user ID found for order {order_number}")
            return

        logger.info(f"📱 Sending confirmation request to customer {customer_user_id}")

        # Import user bot to send message
        from src.bot_instance import bot
        from telebot import types

        # Try to get the original payment message ID for editing
        payment_message_data = get_data(f"user_payment_messages/{order_number}")

        if payment_message_data and payment_message_data.get('message_id'):
            # Check if message is still editable (Telegram restrictions)
            try:
                import datetime
                message_timestamp = payment_message_data.get('timestamp')
                current_time = datetime.datetime.now()

                # Check if we have timestamp and if message is older than 48 hours
                if message_timestamp:
                    try:
                        msg_time = datetime.datetime.fromisoformat(message_timestamp)
                        time_diff = current_time - msg_time
                        if time_diff.total_seconds() > (48 * 3600):  # 48 hours in seconds
                            logger.warning(f"⏰ Payment message for order {order_number} is too old to edit ({time_diff})")
                            raise Exception("Message too old to edit")
                    except (ValueError, TypeError) as e:
                        logger.warning(f"⚠️ Invalid timestamp format for order {order_number}: {e}")
                        # Continue with edit attempt anyway

                logger.info(f"📝 Attempting to edit original payment message for order {order_number}")

                # Get restaurant info
                restaurant_id = order_data.get('restaurant_id')
                restaurant = get_restaurant_by_id(restaurant_id)
                restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"

                # Get original order details for the merged message
                delivery_gate = order_data.get('delivery_gate', 'N/A')
                phone_number = order_data.get('phone_number', 'N/A')
                total_amount = order_data.get('subtotal', 0) + order_data.get('delivery_fee', 0)

                # Format items list
                items = order_data.get('items', [])
                items_text = ""
                for item in items:
                    items_text += f"• {item.get('name', 'Unknown Item')} - {item.get('price', 0)} birr\n"

                # Get points message
                points_earned = order_data.get('points_earned', 0)
                points_message = f"🎁 You earned {points_earned} points!" if points_earned > 0 else ""

                # Create merged message with payment verification + delivery completion
                merged_message = f"""✅ **Payment Verified & Order Delivered!** 🎉

📋 **Order #{order_number}**
🏪 **Restaurant:** {restaurant_name}
📍 **Delivered to:** {delivery_gate}
📞 **Contact:** {phone_number}

🛍️ **Items:**
{items_text.strip()}

💰 **Total Amount:** {total_amount} birr
{points_message}

🚚 **Delivery Status:** Completed
⏰ **Delivered at:** {order_data.get('completed_at', 'N/A')}

🎉 Your order has been delivered by our driver!
Please confirm that you have received your order.

Thank you for choosing Wiz Aroma – Bringing Flavors to Your Doorstep! 😋"""

                # Create inline keyboard with confirmation buttons
                markup = types.InlineKeyboardMarkup()
                confirm_btn = types.InlineKeyboardButton(
                    "✅ Confirm Received",
                    callback_data=f"confirm_delivery_{order_number}"
                )
                not_received_btn = types.InlineKeyboardButton(
                    "❌ Order Not Received",
                    callback_data=f"not_received_{order_number}"
                )
                markup.row(confirm_btn, not_received_btn)

                # Attempt to edit the original payment message
                bot.edit_message_text(
                    merged_message,
                    chat_id=payment_message_data['chat_id'],
                    message_id=payment_message_data['message_id'],
                    reply_markup=markup,
                    parse_mode='Markdown'
                )

                logger.info(f"✅ Successfully edited original payment message for order {order_number}")
                return

            except Exception as edit_error:
                error_msg = str(edit_error).lower()
                if "message can't be edited" in error_msg:
                    logger.warning(f"⚠️ Message editing restricted for order {order_number}: Telegram doesn't allow editing this message")
                elif "message to edit not found" in error_msg:
                    logger.warning(f"⚠️ Original message not found for order {order_number}: Message may have been deleted")
                elif "message too old to edit" in error_msg:
                    logger.warning(f"⚠️ Message too old to edit for order {order_number}: Telegram 48-hour limit exceeded")
                elif "bad request" in error_msg and "can't be edited" in error_msg:
                    logger.warning(f"⚠️ Message structure incompatible for editing order {order_number}: {edit_error}")
                else:
                    logger.error(f"❌ Unexpected error editing payment message for order {order_number}: {edit_error}")

                # Continue to robust fallback mechanism below

        # Fallback: Send new confirmation message if editing failed or no original message found
        logger.info(f"📤 Sending new confirmation message to user {customer_user_id}")

        # Validate customer_user_id
        if not isinstance(customer_user_id, (int, str)) or not str(customer_user_id).isdigit():
            logger.error(f"❌ Invalid customer user ID for order {order_number}: {customer_user_id}")
            return

        # Test bot connection first
        try:
            bot_info = bot.get_me()
            logger.info(f"🤖 User bot connection verified: @{bot_info.username}")
        except Exception as bot_error:
            logger.error(f"❌ User bot connection failed: {bot_error}")
            return

        # Get restaurant info for fallback message
        restaurant_id = order_data.get('restaurant_id')
        restaurant = get_restaurant_by_id(restaurant_id)
        restaurant_name = restaurant['name'] if restaurant else f"Restaurant {restaurant_id}"

        # Create fallback confirmation message
        confirmation_message = f"""🚚 **Delivery Completed!**

📋 **Order #{order_number}**
🏪 **Restaurant**: {restaurant_name}
💰 **Total**: {order_data.get('subtotal', 0)} Birr

Your order has been delivered by our driver.
Please confirm that you have received your order.
"""

        # Create inline keyboard with confirmation buttons
        markup = types.InlineKeyboardMarkup()
        confirm_btn = types.InlineKeyboardButton(
            "✅ Confirm Received",
            callback_data=f"confirm_delivery_{order_number}"
        )
        not_received_btn = types.InlineKeyboardButton(
            "❌ Order Not Received",
            callback_data=f"not_received_{order_number}"
        )
        markup.row(confirm_btn, not_received_btn)

        # Send the confirmation message
        try:
            message_sent = bot.send_message(
                int(customer_user_id),
                confirmation_message,
                parse_mode='Markdown',
                reply_markup=markup
            )

            if message_sent:
                logger.info(f"✅ Successfully sent delivery confirmation request for order {order_number} to user {customer_user_id} (Message ID: {message_sent.message_id})")
            else:
                logger.warning(f"⚠️  Message sent but no response received for order {order_number}")

        except Exception as send_error:
            # Enhanced error handling for customer message sending
            error_message = str(send_error).lower()

            if "chat not found" in error_message:
                logger.warning(f"⚠️  Customer {customer_user_id} - Chat not found. User hasn't started conversation with user bot.")
            elif "forbidden" in error_message or "blocked" in error_message:
                logger.warning(f"⚠️  Customer {customer_user_id} - Bot blocked by user.")
            elif "user not found" in error_message:
                logger.warning(f"⚠️  Customer {customer_user_id} - Invalid Telegram user ID.")
            else:
                logger.error(f"❌ Failed to send confirmation to customer {customer_user_id}: {send_error}")

            # Log the specific error for debugging
            logger.error(f"Customer confirmation send error details: {send_error}")
            raise send_error

    except Exception as e:
        logger.error(f"❌ Error sending customer confirmation request for order {order_number}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Try to log order data for debugging
        try:
            logger.error(f"Order data for debugging: {order_data}")
        except:
            logger.error("Could not log order data for debugging")

def cleanup_completed_order(order_number: str):
    """Clean up order cache and send email notification for completed orders"""
    try:
        # Get order data before cleanup
        confirmed_orders = get_data("confirmed_orders") or {}
        if order_number not in confirmed_orders:
            logger.warning(f"Order {order_number} not found for cleanup")
            return

        order_data = confirmed_orders[order_number]

        # Send email notification for completed order
        try:
            from src.utils.helpers import send_email_notification, format_order_summary
            from src.firebase_db import get_user_names, get_user_phone_numbers

            # Get user info for email
            user_id = order_data.get('user_id')
            user_names = get_user_names()
            user_phones = get_user_phone_numbers()

            user_info = {
                'name': user_names.get(str(user_id), 'Unknown'),
                'phone': user_phones.get(str(user_id), order_data.get('phone_number', 'N/A'))
            }

            # Format email content
            email_subject = f"Order Completed Successfully - #{order_number}"
            email_body = format_order_summary(order_data, user_info)
            email_body += f"\n\n✅ <strong>Order Status:</strong> Successfully Delivered and Confirmed by Customer"
            email_body += f"\n📅 <strong>Completed At:</strong> {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            # Send email notification
            email_sent = send_email_notification(
                "<EMAIL>",
                email_subject,
                email_body
            )

            if email_sent:
                logger.info(f"Completion email sent for order {order_number}")
            else:
                logger.error(f"Failed to send completion email for order {order_number}")

        except Exception as email_error:
            logger.error(f"Error sending completion email for order {order_number}: {email_error}")

        # Clean up order cache from storage
        try:
            # Move order to completed orders collection
            completed_order_data = order_data.copy()
            completed_order_data['completed_at'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            completed_order_data['final_status'] = 'CUSTOMER_CONFIRMED'

            # Store in completed orders
            set_data(f"completed_orders/{order_number}", completed_order_data)

            # Remove from confirmed orders (active cache)
            delete_data(f"confirmed_orders/{order_number}")

            # Clean up any related cache data
            from src.data_storage import clean_up_order_data
            if user_id:
                clean_up_order_data(int(user_id), order_number)

            logger.info(f"Successfully cleaned up order cache for order {order_number}")

        except Exception as cleanup_error:
            logger.error(f"Error cleaning up order cache for order {order_number}: {cleanup_error}")

    except Exception as e:
        logger.error(f"Error in cleanup_completed_order for order {order_number}: {e}")

def notify_customer_confirmed(order_number: str):
    """Notify when customer confirms order completion"""
    try:
        # Get complete order data
        order_data = get_data(f"confirmed_orders/{order_number}")
        if not order_data:
            logger.error(f"Order data not found for {order_number}")
            return

        # Add customer confirmation timing
        confirmation_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        additional_timing = f"• Customer Confirmed: {confirmation_time}\n"

        # Format complete message
        message = format_complete_order_details(
            order_number,
            order_data,
            "ORDER FULLY COMPLETED",
            "Customer has confirmed receipt of the order. Order process complete.",
            additional_timing
        )

        # Send to all authorized tracking users (remove buttons since order is confirmed)
        for user_id in ORDER_TRACK_BOT_AUTHORIZED_IDS:
            try:
                # Try to update existing message first (remove buttons by not including reply_markup)
                if order_data.get('tracking_message_id'):
                    try:
                        order_track_bot.edit_message_text(
                            message,
                            user_id,
                            order_data['tracking_message_id'],
                            reply_markup=None,  # Remove buttons
                            parse_mode='Markdown'
                        )
                        logger.info(f"📤 Updated tracking message for order {order_number} customer confirmation (buttons removed)")
                        continue
                    except Exception as edit_error:
                        logger.warning(f"Failed to edit tracking message, sending new one: {edit_error}")

                # Send new message if edit failed (no buttons needed)
                sent_message = order_track_bot.send_message(user_id, message, parse_mode='Markdown')
                logger.info(f"📤 Sent new tracking message for order {order_number} customer confirmation")

            except Exception as e:
                logger.error(f"❌ Failed to send customer confirmation notification to tracking user {user_id}: {e}")

    except Exception as e:
        logger.error(f"❌ Error in notify_customer_confirmed: {e}")

    # Clean up order cache and send email notification for completed order
    cleanup_completed_order(order_number)


@order_track_bot.callback_query_handler(func=lambda call: call.data.startswith(('admin_confirm_', 'contact_customer_', 'manual_confirm_')))
def handle_tracking_bot_callbacks(call):
    """Handle callback queries for order tracking bot"""
    user_id = call.from_user.id

    # Check authorization
    if not is_authorized(user_id):
        order_track_bot.answer_callback_query(call.id, "❌ Unauthorized access")
        return

    try:
        callback_data = call.data
        logger.info(f"🔘 Order tracking bot callback received from user {user_id}: {callback_data}")

        if callback_data.startswith('admin_confirm_'):
            handle_admin_order_confirmation(call)
        elif callback_data.startswith('contact_customer_'):
            handle_contact_customer(call)
        elif callback_data.startswith('manual_confirm_'):
            # Legacy support for old manual_confirm buttons
            handle_admin_order_confirmation(call)
        else:
            order_track_bot.answer_callback_query(call.id, "❌ Unknown action")

    except Exception as e:
        logger.error(f"❌ Error handling tracking bot callback: {e}")
        order_track_bot.answer_callback_query(call.id, "❌ Error processing request")


def handle_admin_order_confirmation(call):
    """Handle admin order confirmation by authorized personnel"""
    try:
        # Handle both new and legacy callback formats
        if call.data.startswith('admin_confirm_'):
            order_number = call.data.replace('admin_confirm_', '')
        else:
            order_number = call.data.replace('manual_confirm_', '')

        user_id = call.from_user.id

        logger.info(f"🔧 Admin order confirmation requested for order {order_number} by user {user_id}")

        # Get order data
        order_data = get_data(f"confirmed_orders/{order_number}")
        if not order_data:
            order_track_bot.answer_callback_query(call.id, "❌ Order not found")
            return

        # Check if order is in the right status for admin confirmation
        delivery_status = order_data.get('delivery_status', '')
        if delivery_status != 'completed':
            order_track_bot.answer_callback_query(call.id, "❌ Order not ready for confirmation - delivery must be completed first")
            return

        # Check if already confirmed
        if delivery_status == 'customer_confirmed' or order_data.get('status') == 'CUSTOMER_CONFIRMED':
            order_track_bot.answer_callback_query(call.id, "✅ Order already confirmed")
            return

        # Update order status to admin confirmed
        current_timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        order_data['status'] = 'CUSTOMER_CONFIRMED'
        order_data['delivery_status'] = 'customer_confirmed'
        order_data['customer_confirmed_at'] = current_timestamp
        order_data['admin_confirmed_by'] = user_id
        order_data['admin_confirmation'] = True
        order_data['confirmation_type'] = 'admin'

        # Save updated order data
        set_data(f"confirmed_orders/{order_number}", order_data)

        # Send admin confirmation notification to customer
        send_admin_confirmation_notification_to_customer(order_number, order_data, user_id)

        # Trigger customer confirmation workflow (includes analytics counter updates)
        logger.info(f"🔧 Processing admin confirmation for order {order_number} - analytics counters will be updated")
        process_customer_confirmation(order_number, order_data)

        # Update the tracking bot message to show admin confirmation
        send_order_status_update(
            order_number,
            "ORDER CONFIRMED BY ADMIN",
            f"Order has been confirmed as delivered by admin. Customer notified.",
            replace_previous=True
        )

        order_track_bot.answer_callback_query(call.id, "✅ Order confirmed by admin!")
        logger.info(f"✅ Order {order_number} confirmed by admin user {user_id}")

    except Exception as e:
        logger.error(f"❌ Error in admin order confirmation: {e}")
        order_track_bot.answer_callback_query(call.id, "❌ Error confirming order")


def handle_contact_customer(call):
    """Handle contact customer button click"""
    try:
        order_number = call.data.replace('contact_customer_', '')
        user_id = call.from_user.id

        logger.info(f"📞 Contact customer requested for order {order_number} by user {user_id}")

        # Get order data
        order_data = get_data(f"confirmed_orders/{order_number}")
        if not order_data:
            order_track_bot.answer_callback_query(call.id, "❌ Order not found")
            return

        # Get customer contact information
        customer_phone = order_data.get('phone_number', 'N/A')
        customer_name = order_data.get('delivery_name', 'N/A')

        contact_info = f"""📞 **Customer Contact Information**

📋 **Order:** #{order_number}
👤 **Customer:** {customer_name}
📱 **Phone:** {customer_phone}

💡 **Suggested Actions:**
• Call customer to confirm delivery
• Ask about any delivery issues
• Verify delivery location if needed"""

        order_track_bot.send_message(
            user_id,
            contact_info,
            parse_mode='Markdown'
        )

        order_track_bot.answer_callback_query(call.id, "📞 Customer contact info sent")
        logger.info(f"📞 Customer contact info sent for order {order_number} to user {user_id}")

    except Exception as e:
        logger.error(f"❌ Error in contact customer: {e}")
        order_track_bot.answer_callback_query(call.id, "❌ Error getting contact info")


def send_admin_confirmation_notification_to_customer(order_number: str, order_data: dict, admin_user_id: int):
    """Send notification to customer when admin confirms order completion"""
    try:
        customer_user_id = order_data.get('user_id')
        if not customer_user_id:
            # Extract from order number if not in data
            customer_user_id = order_number.split('_')[0]

        if not customer_user_id:
            logger.error(f"Cannot send admin confirmation notification: no customer user ID for order {order_number}")
            return

        # Import user bot to send message
        from src.bot_instance import bot

        # First, try to remove confirmation buttons from any existing customer messages
        remove_customer_confirmation_buttons(order_number, customer_user_id)

        # Format customer notification message
        notification_message = f"""✅ **Order Delivery Confirmed**

📋 **Order #{order_number}**
🔧 **Status:** Your order has been confirmed as delivered by our admin team

📅 **Confirmed at:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎉 **Order process complete!** Thank you for using Wiz Aroma!

If you have any questions about this order, please contact our support team."""

        # Send completion notification to customer
        try:
            bot.send_message(
                customer_user_id,
                notification_message,
                parse_mode='Markdown'
            )
            logger.info(f"✅ Admin confirmation notification sent to customer {customer_user_id}")
        except Exception as e:
            logger.error(f"Failed to send admin confirmation notification to customer {customer_user_id}: {e}")

    except Exception as e:
        logger.error(f"Error sending admin confirmation notification: {e}")


def send_manual_confirmation_notification_to_customer(order_number: str, order_data: dict, admin_user_id: int):
    """Send completion notification to customer when order is manually confirmed"""
    try:
        logger.info(f"📱 Sending manual confirmation notification to customer for order {order_number}")

        # Extract customer user ID
        customer_user_id = order_data.get('user_id')
        if not customer_user_id:
            try:
                customer_user_id = order_number.split('_')[0]
                logger.info(f"🔍 Extracted customer user ID from order number: {customer_user_id}")
            except (IndexError, ValueError):
                logger.error(f"❌ Could not extract user ID from order number: {order_number}")
                return

        if not customer_user_id:
            logger.warning(f"No customer user ID found for order {order_number}")
            return

        # Import user bot to send message
        from src.bot_instance import bot

        # Get delivery personnel information
        delivery_person_info = get_delivery_person_info_for_notification(order_data)

        # Format customer notification message
        notification_message = format_manual_confirmation_customer_message(
            order_number,
            order_data,
            delivery_person_info
        )

        # First, try to remove confirmation buttons from any existing customer messages
        remove_customer_confirmation_buttons(order_number, customer_user_id)

        # Send completion notification to customer
        try:
            bot.send_message(
                customer_user_id,
                notification_message,
                parse_mode='Markdown'
            )
            logger.info(f"✅ Manual confirmation notification sent to customer {customer_user_id}")

        except Exception as send_error:
            logger.error(f"❌ Failed to send manual confirmation notification to customer: {send_error}")

            # Try sending fallback message without markdown
            try:
                fallback_message = f"""Order #{order_number} Completed

Your order has been marked as completed by our administration team.

If you have any concerns about this order, please contact our customer service at:
📞 Phone: 0909782606
📧 Email: <EMAIL>

Thank you for choosing Wiz Aroma!"""

                bot.send_message(customer_user_id, fallback_message)
                logger.info(f"✅ Fallback notification sent to customer {customer_user_id}")

            except Exception as fallback_error:
                logger.error(f"❌ Failed to send fallback notification: {fallback_error}")

    except Exception as e:
        logger.error(f"❌ Error sending manual confirmation notification to customer: {e}")


def get_delivery_person_info_for_notification(order_data: dict) -> dict:
    """Get delivery person information for customer notification"""
    try:
        assigned_to = order_data.get('assigned_to')
        if not assigned_to:
            return {
                'name': 'Our delivery team',
                'phone': 'N/A',
                'found': False
            }

        from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
        personnel = get_delivery_personnel_by_id(assigned_to)

        if personnel:
            return {
                'name': personnel.get('name', 'Our delivery team'),
                'phone': personnel.get('phone', 'N/A'),
                'id': assigned_to,
                'found': True
            }
        else:
            return {
                'name': f'Delivery Personnel {assigned_to}',
                'phone': 'N/A',
                'id': assigned_to,
                'found': False
            }

    except Exception as e:
        logger.error(f"Error getting delivery person info: {e}")
        return {
            'name': 'Our delivery team',
            'phone': 'N/A',
            'found': False
        }


def format_manual_confirmation_customer_message(order_number: str, order_data: dict, delivery_info: dict) -> str:
    """Format the customer notification message for manual confirmation"""
    try:
        # Get restaurant information
        restaurant_id = order_data.get('restaurant_id')
        restaurant = get_restaurant_by_id(restaurant_id) if restaurant_id else None
        restaurant_name = restaurant.get('name', 'Unknown Restaurant') if restaurant else 'Unknown Restaurant'

        # Get order details
        total_amount = order_data.get('subtotal', 0) + order_data.get('delivery_fee', 0)
        completed_at = order_data.get('completed_at', 'N/A')

        # Format delivery person information
        if delivery_info.get('found'):
            delivery_text = f"**Delivered by:** {delivery_info['name']}"
            if delivery_info.get('phone') and delivery_info['phone'] != 'N/A':
                delivery_text += f" ({delivery_info['phone']})"
        else:
            delivery_text = f"**Delivered by:** {delivery_info['name']}"

        # Create the message
        message = f"""✅ **Order Completed - Administrative Confirmation**

📋 **Order #{order_number}**
🏪 **Restaurant:** {restaurant_name}
💰 **Total Amount:** {total_amount} Birr
⏰ **Completed:** {completed_at}

🚚 {delivery_text}

📋 **Status Update:**
Your order has been marked as completed by our administration team. This typically happens when:
• Delivery was confirmed through alternative means
• Technical issues prevented automatic confirmation
• Customer service intervention was required

✅ **Order Process Complete**
Your order is now fully processed and closed in our system.

📞 **Need Help?**
If you have any questions or concerns about this order, please contact our customer service:
• **Phone:** 0909782606
• **Email:** <EMAIL>

Thank you for choosing Wiz Aroma – Bringing Flavors to Your Doorstep! 😋"""

        return message

    except Exception as e:
        logger.error(f"Error formatting manual confirmation customer message: {e}")
        return f"""✅ Order #{order_number} Completed

Your order has been marked as completed by our administration team.

If you have any questions, please contact us at:
📞 Phone: 0909782606
📧 Email: <EMAIL>

Thank you for choosing Wiz Aroma!"""


def remove_customer_confirmation_buttons(order_number: str, customer_user_id: str):
    """Remove confirmation buttons from customer's messages"""
    try:
        logger.info(f"🔄 Attempting to remove confirmation buttons for order {order_number}")

        from src.bot_instance import bot

        # Try to get the payment message data to remove buttons
        payment_message_data = get_data(f"user_payment_messages/{order_number}")

        if payment_message_data and payment_message_data.get('message_id'):
            try:
                # Get the current message text (without buttons)
                current_message = payment_message_data.get('message_text', '')

                # If we don't have the stored message text, create a simple completion message
                if not current_message:
                    restaurant_id = get_data(f"confirmed_orders/{order_number}", {}).get('restaurant_id')
                    restaurant = get_restaurant_by_id(restaurant_id) if restaurant_id else None
                    restaurant_name = restaurant.get('name', 'Restaurant') if restaurant else 'Restaurant'

                    current_message = f"""✅ **Order Completed**

📋 **Order #{order_number}**
🏪 **Restaurant:** {restaurant_name}

✅ **Status:** Order has been completed and confirmed.

Thank you for choosing Wiz Aroma! 😋"""

                # Edit message to remove buttons
                bot.edit_message_text(
                    current_message,
                    chat_id=payment_message_data['chat_id'],
                    message_id=payment_message_data['message_id'],
                    reply_markup=None,  # Remove buttons
                    parse_mode='Markdown'
                )

                logger.info(f"✅ Successfully removed confirmation buttons from customer message for order {order_number}")

            except Exception as edit_error:
                logger.warning(f"⚠️ Could not edit customer message to remove buttons: {edit_error}")
                # This is not critical - the new completion message will still be sent
        else:
            logger.info(f"ℹ️ No payment message data found for order {order_number} - buttons may have already been removed")

    except Exception as e:
        logger.error(f"❌ Error removing customer confirmation buttons: {e}")


def process_customer_confirmation(order_number: str, order_data: dict):
    """Process customer confirmation workflow (manual or automatic)"""
    try:
        # Notify order tracking bot about confirmation
        notify_customer_confirmed(order_number)

        # Update analytics counters for completed order
        try:
            from src.bots.management_bot import update_order_analytics_counters

            # Determine order category based on whether it had issues
            had_issue = (order_data.get('issue_reported_at') or
                        order_data.get('delivery_status') == 'delivery_issue' or
                        order_data.get('analytics_status') in ['completed_after_issue_resolution', 'resolved_pending_confirmation'])

            if had_issue:
                # Order had issues but was resolved - count as complete
                update_order_analytics_counters('complete', 'increment')
                logger.info(f"📊 Updated analytics counters: order {order_number} completed after issue resolution")
            else:
                # Normal completion
                update_order_analytics_counters('complete', 'increment')
                logger.info(f"📊 Updated analytics counters: order {order_number} completed normally")

        except Exception as analytics_error:
            logger.error(f"Failed to update analytics counters for order {order_number}: {analytics_error}")

        # Clean up order cache and move to completed orders
        cleanup_completed_order(order_number)

        # Send email notification for completed order
        try:
            from src.utils.email_utils import send_order_completion_email
            send_order_completion_email(order_data)
        except Exception as email_error:
            logger.warning(f"Failed to send completion email for order {order_number}: {email_error}")

        logger.info(f"✅ Customer confirmation workflow completed for order {order_number}")

    except Exception as e:
        logger.error(f"❌ Error in customer confirmation workflow: {e}")


def run_order_track_bot():
    """Run the order tracking bot"""
    logger.info("Starting Order Tracking Bot...")
    try:
        order_track_bot.polling(none_stop=True, interval=1)
    except Exception as e:
        logger.error(f"Order Tracking Bot error: {e}")
        raise

if __name__ == "__main__":
    run_order_track_bot()