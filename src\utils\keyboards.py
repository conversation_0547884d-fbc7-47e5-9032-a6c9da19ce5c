"""
Keyboard markup creation functions for the Wiz Aroma Delivery Bot.
"""

from telebot import types
from src.config import restaurants, delivery_fees
from src.data_storage import get_all_areas, get_all_delivery_locations, get_delivery_fee
import logging

logger = logging.getLogger(__name__)


def get_main_menu_markup():
    """Create and return the main menu markup"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("🍽️ Order Food"),
        types.KeyboardButton("⭐ Favorite Orders"),
        types.KeyboardButton("💫 My Points"),
        types.KeyboardButton("ℹ️ Help"),
    )
    return markup


def get_areas_markup():
    """Create and return the areas markup"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)

    # Add area buttons from data storage instead of static config
    areas = get_all_areas()

    # If no areas found from data storage, use static config
    if not areas:
        from src.config import restaurants as config_restaurants

        for area_name in config_restaurants:
            markup.add(types.KeyboardButton(f"📍 {area_name}"))
        logger.info("Using static config areas for markup (no data in storage)")
    else:
        # Use areas from data storage
        for area in areas:
            markup.add(types.KeyboardButton(f"📍 {area['name']}"))
        logger.info(f"Using {len(areas)} areas from data storage for markup")

    # Add back button
    markup.add(types.KeyboardButton("🔙 Back to Main Menu"))

    return markup


def get_restaurants_markup(area):
    """Create and return the restaurants markup for a specific area"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    has_restaurants = False

    # Check if area is a string (area name) or a list of restaurants
    if isinstance(area, list):
        # Area is already a list of restaurants (from get_restaurants_by_area)
        restaurants_list = area
        if restaurants_list:
            for restaurant in restaurants_list:
                restaurant_text = f"🏪 {restaurant['name']}"
                markup.add(types.KeyboardButton(restaurant_text))
            has_restaurants = True
            logger.info(
                f"Added {len(restaurants_list)} restaurants to markup from list"
            )
    else:
        # Area is a string (area name) - legacy support
        # Try to find the area in the static config
        from src.config import restaurants as config_restaurants

        if area in config_restaurants:
            for _, res_info in config_restaurants[area].items():
                restaurant_text = f"🏪 {res_info['name']}"
                markup.add(types.KeyboardButton(restaurant_text))
            has_restaurants = True
            logger.info(
                f"Added restaurants to markup from static config for area '{area}'"
            )

    # If no restaurants were added, show a message
    if not has_restaurants:
        markup.add(types.KeyboardButton("No restaurants available"))
        logger.warning(f"No restaurants found for area: {area}")

    # Add back button
    markup.add(types.KeyboardButton("🔙 Back to Areas"))

    return markup


def get_menu_items_markup(restaurant_id, items):
    """Create and return the menu items markup for a specific restaurant

    Args:
        restaurant_id: The ID of the restaurant
        items: List of menu items from get_restaurant_menu function
    """
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)

    # Add menu items
    for item in items:
        # Make sure the item name and price are consistent with how we parse them
        item_text = f"➕ {item['name']} ({item['price']} birr)"
        markup.add(types.KeyboardButton(item_text))

    # Add control buttons
    markup.add(
        types.KeyboardButton("✅ Complete Order"),
        types.KeyboardButton("❌ Cancel Order"),
        types.KeyboardButton("🔙 Back to Areas"),
    )

    return markup


def get_delivery_gates_markup(restaurant_area, restaurant_area_id):
    """Create and return the delivery gates markup for a specific area

    Args:
        restaurant_area: The name of the area
        restaurant_area_id: The ID of the area
    """
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)

    # Ensure restaurant_area_id is an integer
    try:
        restaurant_area_id = int(restaurant_area_id)
        logger.info(f"Creating delivery gates markup for area: {restaurant_area} (ID: {restaurant_area_id})")
    except (ValueError, TypeError) as e:
        logger.error(
            f"Failed to convert restaurant_area_id to integer in get_delivery_gates_markup: {e}"
        )
        logger.error(
            f"Original restaurant_area_id: {restaurant_area_id} ({type(restaurant_area_id)})"
        )
        # Add cancel button and return early if area ID is invalid
        markup.add(types.KeyboardButton("❌ Cancel Order"))
        return markup

    # Load fresh data from Firebase to ensure real-time consistency
    try:
        from src.firebase_db import get_data

        # Refresh delivery locations and fees from Firebase
        delivery_locations_data = get_data("delivery_locations") or {"delivery_locations": []}
        delivery_fees_data = get_data("delivery_fees") or {"delivery_fees": []}

        all_locations = delivery_locations_data.get("delivery_locations", [])
        all_fees = delivery_fees_data.get("delivery_fees", [])

        logger.info(f"Loaded fresh data from Firebase: {len(all_locations)} delivery locations, {len(all_fees)} delivery fees")

    except Exception as e:
        logger.error(f"Error loading fresh data from Firebase: {e}")
        # Fallback to cached data
        all_locations = get_all_delivery_locations()
        logger.warning(f"Using cached data: {len(all_locations)} delivery locations")

    # Add delivery gates for the restaurant's area - ONLY from Firebase data with validation
    try:
        from src.utils.data_sync import get_valid_delivery_locations_for_area

        # Get only locations that have valid delivery fees for this area
        valid_locations = get_valid_delivery_locations_for_area(restaurant_area_id)

        for location in valid_locations:
            location_id = location["id"]
            location_name = location["name"]
            fee = location["fee"]

            # Store the full location name in the button text
            button_text = f"🚪 {location_name} ({fee} birr)"
            logger.info(f"Adding validated location to markup: {location_name} (ID: {location_id}) with fee {fee} birr for area {restaurant_area_id}")
            markup.add(types.KeyboardButton(button_text))

        # Log summary of what was added
        logger.info(f"Added {len(valid_locations)} validated delivery locations for area {restaurant_area_id} ({restaurant_area})")

        # If no valid locations found, log detailed warning
        if len(valid_locations) == 0:
            logger.warning(f"No valid delivery locations found for area {restaurant_area_id} ({restaurant_area}). This means:")
            logger.warning("  1. No delivery locations exist in Firebase, OR")
            logger.warning("  2. No delivery fees are configured for this area, OR")
            logger.warning("  3. Area/location ID mismatch in Firebase data")
            logger.warning("  Use maintenance bot to add delivery locations and fees.")

    except Exception as e:
        logger.error(f"Error getting valid delivery locations for area {restaurant_area_id}: {e}")
        # Fallback to old method if data sync fails
        logger.warning("Falling back to basic delivery location loading...")

        for location in all_locations:
            try:
                location_id = int(location["id"])
                location_name = location["name"]
                fee = get_delivery_fee(restaurant_area_id, location_id)

                if fee > 0:
                    button_text = f"🚪 {location_name} ({fee} birr)"
                    logger.info(f"Adding fallback location: {location_name} (ID: {location_id}) with fee {fee} birr")
                    markup.add(types.KeyboardButton(button_text))
            except (ValueError, TypeError) as e:
                logger.error(f"Error processing fallback location {location.get('name', 'Unknown')}: {e}")

    markup.add(types.KeyboardButton("❌ Cancel Order"))

    return markup


def get_order_description_markup():
    """Create and return the order description markup"""
    markup = types.ReplyKeyboardMarkup(row_width=1, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("⏩ Skip Description"),
        types.KeyboardButton("❌ Cancel Order"),
    )
    return markup


def get_delivery_name_markup(saved_name=None):
    """Create and return the delivery name markup"""
    markup = types.ReplyKeyboardMarkup(row_width=1, resize_keyboard=True)
    markup.add(types.KeyboardButton("❌ Cancel Order"))

    # Add saved name if available
    if saved_name and isinstance(saved_name, str) and saved_name.strip():
        markup.add(types.KeyboardButton(f"📝 Use saved name: {saved_name}"))
        # Log that we added the saved name button
        logging.info(f"Added saved name button: {saved_name}")
    else:
        # Log that we did not add the saved name button
        logging.info(f"No saved name button added. Value was: {saved_name}")

    return markup


def get_phone_number_markup(saved_phone=None):
    """Create and return the phone number markup"""
    markup = types.ReplyKeyboardMarkup(row_width=1, resize_keyboard=True)
    markup.add(types.KeyboardButton("❌ Cancel Order"))

    # Add saved phone if available
    if saved_phone and isinstance(saved_phone, str) and saved_phone.strip():
        markup.add(types.KeyboardButton(f"📱 Use saved number: {saved_phone}"))
        # Log that we added the saved phone button
        logging.info(f"Added saved phone button: {saved_phone}")
    else:
        # Log that we did not add the saved phone button
        logging.info(f"No saved phone button added. Value was: {saved_phone}")

    return markup


def get_email_markup(saved_email=None):
    """Create and return the email input markup"""
    markup = types.ReplyKeyboardMarkup(row_width=1, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Skip Email (Optional)"),
        types.KeyboardButton("❌ Cancel Order"),
    )

    # Add saved email if available
    if saved_email:
        markup.add(types.KeyboardButton(f"📧 Use saved email: {saved_email}"))

    return markup


def get_order_confirmation_markup():
    """Create and return the order confirmation markup"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("✅ Confirm Order"),
        types.KeyboardButton("💾 Save as Favorite"),
        types.KeyboardButton("🔄 Update Menu"),
        types.KeyboardButton("❌ Cancel Order"),
    )
    return markup


def get_payment_method_markup(has_enough_points=False):
    """Create and return the payment method markup"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)

    # Show points option only if sufficient balance
    if has_enough_points:
        markup.add(types.KeyboardButton("✅ Use Points"))

    # Add mobile money and bank options
    markup.add(
        types.KeyboardButton("📱 Telebirr"),
        types.KeyboardButton("🏦 CBE Bank"),
    )

    # Add BOA Bank option
    markup.add(types.KeyboardButton("🏦 BOA Bank"))

    markup.add(types.KeyboardButton("❌ Cancel Order"))
    return markup


def get_points_confirmation_markup():
    """Create and return the points confirmation markup"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("✅ Confirm Use Points"),
        types.KeyboardButton("❌ Cancel Points Use"),
    )
    return markup


def get_payment_receipt_markup():
    """Create and return the payment receipt markup"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("🔄 Back to Bank Options"),
        types.KeyboardButton("❌ Cancel Order"),
    )
    return markup


def get_admin_action_markup(order_number):
    """Create and return the admin action markup"""
    markup = types.InlineKeyboardMarkup()
    markup.row(
        types.InlineKeyboardButton(
            "✅ Approve", callback_data=f"initial_approve_{order_number}"
        ),
        types.InlineKeyboardButton(
            "❌ Reject", callback_data=f"initial_reject_{order_number}"
        ),
    )
    markup.row(
        types.InlineKeyboardButton(
            "✍️ Add Remarks", callback_data=f"initial_remark_{order_number}"
        )
    )
    return markup


def get_finance_action_markup(user_id, order_number):
    """Create and return the finance action markup"""
    markup = types.InlineKeyboardMarkup()
    markup.row(
        types.InlineKeyboardButton(
            "✅ Approve",
            callback_data=f"verify_approve_{user_id}_{order_number}",
        ),
        types.InlineKeyboardButton(
            "❌ Reject",
            callback_data=f"verify_reject_{user_id}_{order_number}",
        ),
    )
    return markup


def get_favorite_orders_markup(num_favorites, favorite_orders_list=None):
    """Create and return the favorite orders markup"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)

    # Add buttons for each favorite order
    if favorite_orders_list:
        for i, favorite in enumerate(favorite_orders_list):
            order_name = favorite.get("favorite_name", f"Order #{i+1}")
            markup.add(types.KeyboardButton(f"🔄 {order_name}"))
    else:
        # Fallback to generic numbering if no list provided
        for i in range(num_favorites):
            markup.add(types.KeyboardButton(f"🔄 Order #{i+1}"))

    # Add button to delete favorite order if any exist
    if num_favorites > 0:
        markup.add(types.KeyboardButton("🗑️ Delete Favorite Order"))

    # Add back button
    markup.add(types.KeyboardButton("🔙 Back to Main Menu"))

    return markup


def get_delete_favorite_markup(num_favorites, favorite_orders_list=None):
    """Create and return the delete favorite orders markup"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)

    # Add buttons for each favorite order
    if favorite_orders_list:
        for i, favorite in enumerate(favorite_orders_list):
            order_name = favorite.get("favorite_name", f"Order #{i+1}")
            markup.add(types.KeyboardButton(f"🗑️ Delete: {order_name}"))
    else:
        # Fallback to generic numbering if no list provided
        for i in range(num_favorites):
            markup.add(types.KeyboardButton(f"🗑️ Delete Order #{i+1}"))

    # Add back button
    markup.add(types.KeyboardButton("🔙 Back to Favorite Orders"))

    return markup


def get_save_favorite_markup():
    """Create and return the markup for saving favorite order"""
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("💾 Save as Favorite"),
        types.KeyboardButton("📨 Submit Order"),
        types.KeyboardButton("❌ Cancel Order"),
    )
    return markup
